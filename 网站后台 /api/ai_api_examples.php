<?php
/**
 * AI API使用示例
 * 展示如何使用DeepSeek和豆包API接口
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 示例配置
$config = [
    'base_url' => 'http://localhost/网站后台 /api',
    'license_key' => 'your_license_key_here', // 请替换为实际的license key
    'deepseek_api_key' => 'sk-your-deepseek-key', // 请替换为实际的DeepSeek API key
    'doubao_api_key' => 'your-doubao-key' // 请替换为实际的豆包API key
];

/**
 * 发送HTTP请求的辅助函数
 */
function apiRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($method !== 'GET') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    }
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'data' => json_decode($response, true)
    ];
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>AI API使用示例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .example { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .code { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .result { background: #e6fffa; padding: 10px; border-left: 4px solid #38b2ac; margin: 10px 0; }
        .error { background: #fed7d7; border-left: 4px solid #e53e3e; }
        .success { background: #c6f6d5; border-left: 4px solid #38a169; }
        h1, h2, h3 { color: #2d3748; }
        button { background: #4299e1; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #3182ce; }
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { padding: 10px 20px; background: #edf2f7; margin-right: 5px; cursor: pointer; border-radius: 5px 5px 0 0; }
        .tab.active { background: #4299e1; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI API使用示例</h1>
        <p>本页面展示如何使用DeepSeek和豆包AI API接口</p>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('overview')">概览</div>
            <div class="tab" onclick="showTab('deepseek')">DeepSeek API</div>
            <div class="tab" onclick="showTab('doubao')">豆包 API</div>
            <div class="tab" onclick="showTab('manager')">服务管理</div>
            <div class="tab" onclick="showTab('integration')">集成示例</div>
        </div>
        
        <!-- 概览标签页 -->
        <div id="overview" class="tab-content active">
            <h2>API概览</h2>
            <p>我们提供了三个主要的API接口：</p>
            <ul>
                <li><strong>DeepSeek API</strong> - 管理DeepSeek AI服务</li>
                <li><strong>豆包 API</strong> - 管理豆包AI服务</li>
                <li><strong>AI服务管理器</strong> - 统一管理所有AI服务</li>
            </ul>
            
            <h3>认证方式</h3>
            <p>所有API都需要有效的license_key进行认证：</p>
            <div class="code">
GET /api/deepseek_api.php?action=status&license_key=your_license_key
POST /api/deepseek_api.php?action=chat
Content-Type: application/json
{
    "license_key": "your_license_key",
    "message": "你好"
}
            </div>
            
            <h3>响应格式</h3>
            <p>所有API都返回统一的JSON格式：</p>
            <div class="code">
{
    "success": true,
    "message": "操作成功",
    "data": { ... }
}
            </div>
        </div>
        
        <!-- DeepSeek API标签页 -->
        <div id="deepseek" class="tab-content">
            <h2>DeepSeek API示例</h2>
            
            <div class="example">
                <h3>1. 获取DeepSeek状态</h3>
                <button onclick="testDeepSeekStatus()">测试</button>
                <div class="code">
GET /api/deepseek_api.php?action=status&license_key=<?php echo $config['license_key']; ?>
                </div>
                <div id="deepseek-status-result" class="result"></div>
            </div>
            
            <div class="example">
                <h3>2. 获取DeepSeek配置</h3>
                <button onclick="testDeepSeekConfig()">测试</button>
                <div class="code">
GET /api/deepseek_api.php?action=config&license_key=<?php echo $config['license_key']; ?>
                </div>
                <div id="deepseek-config-result" class="result"></div>
            </div>
            
            <div class="example">
                <h3>3. 更新DeepSeek配置</h3>
                <button onclick="testDeepSeekUpdate()">测试</button>
                <div class="code">
PUT /api/deepseek_api.php?action=config
Content-Type: application/json
{
    "license_key": "<?php echo $config['license_key']; ?>",
    "enabled": true,
    "model": "deepseek-chat",
    "thinking_enabled": false,
    "reply_delay": 0,
    "system_prompt": "你是一名专业的客服人员。"
}
                </div>
                <div id="deepseek-update-result" class="result"></div>
            </div>
            
            <div class="example">
                <h3>4. 添加API密钥</h3>
                <button onclick="testDeepSeekAddKey()">测试</button>
                <div class="code">
PUT /api/deepseek_api.php?action=add_api_key
Content-Type: application/json
{
    "license_key": "<?php echo $config['license_key']; ?>",
    "api_key": "<?php echo $config['deepseek_api_key']; ?>"
}
                </div>
                <div id="deepseek-addkey-result" class="result"></div>
            </div>
        </div>
        
        <!-- 豆包API标签页 -->
        <div id="doubao" class="tab-content">
            <h2>豆包API示例</h2>
            
            <div class="example">
                <h3>1. 获取豆包状态</h3>
                <button onclick="testDoubaoStatus()">测试</button>
                <div class="code">
GET /api/doubao_api.php?action=status&license_key=<?php echo $config['license_key']; ?>
                </div>
                <div id="doubao-status-result" class="result"></div>
            </div>
            
            <div class="example">
                <h3>2. 获取可用模型</h3>
                <button onclick="testDoubaoModels()">测试</button>
                <div class="code">
GET /api/doubao_api.php?action=models&license_key=<?php echo $config['license_key']; ?>
                </div>
                <div id="doubao-models-result" class="result"></div>
            </div>
            
            <div class="example">
                <h3>3. 更新豆包配置</h3>
                <button onclick="testDoubaoUpdate()">测试</button>
                <div class="code">
PUT /api/doubao_api.php?action=config
Content-Type: application/json
{
    "license_key": "<?php echo $config['license_key']; ?>",
    "enabled": true,
    "model": "doubao-seed-1-6-250615",
    "thinking_enabled": false,
    "reply_delay": 0,
    "system_prompt": "你是一名专业的客服人员。"
}
                </div>
                <div id="doubao-update-result" class="result"></div>
            </div>
        </div>
        
        <!-- 服务管理标签页 -->
        <div id="manager" class="tab-content">
            <h2>AI服务管理器示例</h2>
            
            <div class="example">
                <h3>1. 获取所有服务状态</h3>
                <button onclick="testManagerStatus()">测试</button>
                <div class="code">
GET /api/ai_service_manager.php?action=status&license_key=<?php echo $config['license_key']; ?>
                </div>
                <div id="manager-status-result" class="result"></div>
            </div>
            
            <div class="example">
                <h3>2. 获取所有配置</h3>
                <button onclick="testManagerConfigs()">测试</button>
                <div class="code">
GET /api/ai_service_manager.php?action=configs&license_key=<?php echo $config['license_key']; ?>
                </div>
                <div id="manager-configs-result" class="result"></div>
            </div>
            
            <div class="example">
                <h3>3. 同步前端配置</h3>
                <button onclick="testManagerSync()">测试</button>
                <div class="code">
POST /api/ai_service_manager.php?action=sync_from_frontend
Content-Type: application/json
{
    "license_key": "<?php echo $config['license_key']; ?>",
    "ai_settings": {
        "deepseek": {
            "enabled": true,
            "model": "deepseek-chat",
            "thinkingEnabled": false,
            "replyDelay": 0,
            "systemPrompt": "你是一名专业的客服人员。",
            "apiKeys": ["<?php echo $config['deepseek_api_key']; ?>"]
        },
        "doubao": {
            "enabled": true,
            "model": "doubao-seed-1-6-250615",
            "thinkingEnabled": false,
            "replyDelay": 0,
            "systemPrompt": "你是一名专业的客服人员。",
            "apiKeys": ["<?php echo $config['doubao_api_key']; ?>"]
        }
    }
}
                </div>
                <div id="manager-sync-result" class="result"></div>
            </div>
        </div>
        
        <!-- 集成示例标签页 -->
        <div id="integration" class="tab-content">
            <h2>集成示例</h2>
            
            <h3>JavaScript集成</h3>
            <div class="code">
// 获取AI服务状态
async function getAIStatus(licenseKey) {
    const response = await fetch(`/api/ai_service_manager.php?action=status&license_key=${licenseKey}`);
    const result = await response.json();
    return result;
}

// 发送聊天消息
async function sendChatMessage(licenseKey, serviceType, message) {
    const apiUrl = serviceType === 'deepseek' ? 
        '/api/deepseek_api.php?action=chat' : 
        '/api/doubao_api.php?action=chat';
    
    const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            license_key: licenseKey,
            message: message,
            session_id: 'user_session_123'
        })
    });
    
    return await response.json();
}
            </div>
            
            <h3>PHP集成</h3>
            <div class="code">
// 获取AI配置
function getAIConfig($licenseKey) {
    $url = "http://localhost/api/ai_service_manager.php?action=configs&license_key=" . urlencode($licenseKey);
    $response = file_get_contents($url);
    return json_decode($response, true);
}

// 更新AI配置
function updateAIConfig($licenseKey, $serviceType, $config) {
    $apiUrl = $serviceType === 'deepseek' ? 
        'http://localhost/api/deepseek_api.php?action=config' : 
        'http://localhost/api/doubao_api.php?action=config';
    
    $data = array_merge(['license_key' => $licenseKey], $config);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'PUT',
            'header' => 'Content-Type: application/json',
            'content' => json_encode($data)
        ]
    ]);
    
    $response = file_get_contents($apiUrl, false, $context);
    return json_decode($response, true);
}
            </div>
        </div>
    </div>

    <script>
        const config = <?php echo json_encode($config); ?>;
        
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
        }
        
        async function apiRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {}
                };
                
                if (data) {
                    options.headers['Content-Type'] = 'application/json';
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const result = await response.json();
                
                return {
                    success: response.ok,
                    data: result
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.className = 'result ' + (result.success ? 'success' : 'error');
            element.innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
        }
        
        // DeepSeek API测试函数
        async function testDeepSeekStatus() {
            const result = await apiRequest(`${config.base_url}/deepseek_api.php?action=status&license_key=${config.license_key}`);
            displayResult('deepseek-status-result', result);
        }
        
        async function testDeepSeekConfig() {
            const result = await apiRequest(`${config.base_url}/deepseek_api.php?action=config&license_key=${config.license_key}`);
            displayResult('deepseek-config-result', result);
        }
        
        async function testDeepSeekUpdate() {
            const data = {
                license_key: config.license_key,
                enabled: true,
                model: 'deepseek-chat',
                thinking_enabled: false,
                reply_delay: 0,
                system_prompt: '你是一名专业的客服人员。'
            };
            const result = await apiRequest(`${config.base_url}/deepseek_api.php?action=config`, 'PUT', data);
            displayResult('deepseek-update-result', result);
        }
        
        async function testDeepSeekAddKey() {
            const data = {
                license_key: config.license_key,
                api_key: config.deepseek_api_key
            };
            const result = await apiRequest(`${config.base_url}/deepseek_api.php?action=add_api_key`, 'PUT', data);
            displayResult('deepseek-addkey-result', result);
        }
        
        // 豆包API测试函数
        async function testDoubaoStatus() {
            const result = await apiRequest(`${config.base_url}/doubao_api.php?action=status&license_key=${config.license_key}`);
            displayResult('doubao-status-result', result);
        }
        
        async function testDoubaoModels() {
            const result = await apiRequest(`${config.base_url}/doubao_api.php?action=models&license_key=${config.license_key}`);
            displayResult('doubao-models-result', result);
        }
        
        async function testDoubaoUpdate() {
            const data = {
                license_key: config.license_key,
                enabled: true,
                model: 'doubao-seed-1-6-250615',
                thinking_enabled: false,
                reply_delay: 0,
                system_prompt: '你是一名专业的客服人员。'
            };
            const result = await apiRequest(`${config.base_url}/doubao_api.php?action=config`, 'PUT', data);
            displayResult('doubao-update-result', result);
        }
        
        // 服务管理器测试函数
        async function testManagerStatus() {
            const result = await apiRequest(`${config.base_url}/ai_service_manager.php?action=status&license_key=${config.license_key}`);
            displayResult('manager-status-result', result);
        }
        
        async function testManagerConfigs() {
            const result = await apiRequest(`${config.base_url}/ai_service_manager.php?action=configs&license_key=${config.license_key}`);
            displayResult('manager-configs-result', result);
        }
        
        async function testManagerSync() {
            const data = {
                license_key: config.license_key,
                ai_settings: {
                    deepseek: {
                        enabled: true,
                        model: 'deepseek-chat',
                        thinkingEnabled: false,
                        replyDelay: 0,
                        systemPrompt: '你是一名专业的客服人员。',
                        apiKeys: [config.deepseek_api_key]
                    },
                    doubao: {
                        enabled: true,
                        model: 'doubao-seed-1-6-250615',
                        thinkingEnabled: false,
                        replyDelay: 0,
                        systemPrompt: '你是一名专业的客服人员。',
                        apiKeys: [config.doubao_api_key]
                    }
                }
            };
            const result = await apiRequest(`${config.base_url}/ai_service_manager.php?action=sync_from_frontend`, 'POST', data);
            displayResult('manager-sync-result', result);
        }
    </script>
</body>
</html>
