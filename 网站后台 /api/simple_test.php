<?php
/**
 * 简单API测试页面
 * 用于快速测试和诊断API问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI API 测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        input[type="text"] { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin: 5px; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .loading { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI API 测试页面</h1>
        <p>用于测试DeepSeek和豆包API接口的连通性和功能</p>
        
        <!-- 基础信息测试 -->
        <div class="test-section">
            <h3>1. 基础环境检测</h3>
            <button onclick="testBasic()">检测基础环境</button>
            <div id="basic-result" class="result"></div>
        </div>
        
        <!-- 数据库连接测试 -->
        <div class="test-section">
            <h3>2. 数据库连接测试</h3>
            <button onclick="testDatabase()">测试数据库连接</button>
            <div id="db-result" class="result"></div>
        </div>
        
        <!-- DeepSeek API测试 -->
        <div class="test-section">
            <h3>3. DeepSeek API测试</h3>
            <input type="text" id="deepseek-key" placeholder="输入DeepSeek API密钥">
            <button onclick="testDeepSeek()">测试DeepSeek API</button>
            <div id="deepseek-result" class="result"></div>
        </div>
        
        <!-- 豆包API测试 -->
        <div class="test-section">
            <h3>4. 豆包API测试</h3>
            <input type="text" id="doubao-key" placeholder="输入豆包API密钥">
            <button onclick="testDoubao()">测试豆包API</button>
            <div id="doubao-result" class="result"></div>
        </div>
        
        <!-- 完整API接口测试 -->
        <div class="test-section">
            <h3>5. 完整API接口测试</h3>
            <input type="text" id="license-key" placeholder="输入license_key">
            <button onclick="testFullAPI()">测试完整API接口</button>
            <div id="full-result" class="result"></div>
        </div>
    </div>

    <script>
        function showLoading(elementId) {
            document.getElementById(elementId).innerHTML = '<div class="loading">测试中...</div>';
        }
        
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
            element.innerHTML = JSON.stringify(data, null, 2);
        }
        
        function testBasic() {
            showLoading('basic-result');
            
            const basicInfo = {
                php_version: '<?php echo PHP_VERSION; ?>',
                server_software: '<?php echo $_SERVER["SERVER_SOFTWARE"] ?? "未知"; ?>',
                curl_enabled: <?php echo function_exists('curl_init') ? 'true' : 'false'; ?>,
                openssl_enabled: <?php echo extension_loaded('openssl') ? 'true' : 'false'; ?>,
                pdo_enabled: <?php echo extension_loaded('pdo') ? 'true' : 'false'; ?>,
                pdo_mysql_enabled: <?php echo extension_loaded('pdo_mysql') ? 'true' : 'false'; ?>,
                putenv_disabled: <?php echo function_exists('putenv') ? 'false' : 'true'; ?>,
                current_time: '<?php echo date("Y-m-d H:i:s"); ?>',
                timezone: '<?php echo date_default_timezone_get(); ?>'
            };
            
            showResult('basic-result', basicInfo, true);
        }
        
        function testDatabase() {
            showLoading('db-result');
            
            fetch('test_api_fix.php?action=test_db')
                .then(response => response.json())
                .then(data => {
                    showResult('db-result', data, data.success);
                })
                .catch(error => {
                    showResult('db-result', {error: error.message}, false);
                });
        }
        
        function testDeepSeek() {
            const apiKey = document.getElementById('deepseek-key').value;
            if (!apiKey) {
                alert('请输入DeepSeek API密钥');
                return;
            }
            
            showLoading('deepseek-result');
            
            const formData = new FormData();
            formData.append('action', 'test_deepseek');
            formData.append('api_key', apiKey);
            
            fetch('test_api_fix.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    showResult('deepseek-result', data, data.success);
                })
                .catch(error => {
                    showResult('deepseek-result', {error: error.message}, false);
                });
        }
        
        function testDoubao() {
            const apiKey = document.getElementById('doubao-key').value;
            if (!apiKey) {
                alert('请输入豆包API密钥');
                return;
            }
            
            showLoading('doubao-result');
            
            const formData = new FormData();
            formData.append('action', 'test_doubao');
            formData.append('api_key', apiKey);
            
            fetch('test_api_fix.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    showResult('doubao-result', data, data.success);
                })
                .catch(error => {
                    showResult('doubao-result', {error: error.message}, false);
                });
        }
        
        function testFullAPI() {
            const licenseKey = document.getElementById('license-key').value;
            if (!licenseKey) {
                alert('请输入license_key');
                return;
            }
            
            showLoading('full-result');
            
            // 测试DeepSeek API接口
            fetch(`deepseek_api.php?action=info&license_key=${licenseKey}`)
                .then(response => response.json())
                .then(data => {
                    showResult('full-result', {
                        test_type: 'DeepSeek API接口测试',
                        result: data
                    }, data.success);
                })
                .catch(error => {
                    showResult('full-result', {
                        test_type: 'DeepSeek API接口测试',
                        error: error.message
                    }, false);
                });
        }
    </script>
</body>
</html>
