<?php
/**
 * DeepSeek AI客服API接口
 * 提供DeepSeek AI配置管理、API密钥验证、聊天等功能
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// 定义API访问常量
define('API_ACCESS', true);

// 引入基础类和配置
require_once __DIR__ . '/api_base.php';
require_once __DIR__ . '/config.php';

class DeepSeekAPI extends ApiBase {
    
    private $encryptionKey;
    
    public function __construct() {
        parent::__construct();
        
        // 获取加密密钥
        $this->encryptionKey = $this->getEncryptionKey();
        
        // 处理请求
        $this->handleRequest();
    }
    
    /**
     * 处理API请求
     */
    private function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        // 验证卡密
        $licenseKey = $this->request['license_key'] ?? $_GET['license_key'] ?? '';
        if (empty($licenseKey)) {
            $this->respondError('缺少license_key参数', 400);
        }
        
        $licenseData = $this->validateLicenseKey($licenseKey);
        if (!$licenseData) {
            $this->respondError('无效的license_key', 403);
        }
        
        // 路由处理
        switch ($method) {
            case 'GET':
                $this->handleGetRequest($action, $licenseData);
                break;
            case 'POST':
                $this->handlePostRequest($action, $licenseData);
                break;
            case 'PUT':
                $this->handlePutRequest($action, $licenseData);
                break;
            case 'DELETE':
                $this->handleDeleteRequest($action, $licenseData);
                break;
            default:
                $this->respondError('不支持的请求方法', 405);
        }
    }
    
    /**
     * 处理GET请求
     */
    private function handleGetRequest($action, $licenseData) {
        switch ($action) {
            case 'config':
                $this->getConfig($licenseData);
                break;
            case 'status':
                $this->getStatus($licenseData);
                break;
            case 'api_keys':
                $this->getApiKeys($licenseData);
                break;
            case 'models':
                $this->getAvailableModels();
                break;
            case 'info':
                $this->getApiInfo();
                break;
            default:
                $this->respondError('未知的操作类型', 400);
        }
    }
    
    /**
     * 处理POST请求
     */
    private function handlePostRequest($action, $licenseData) {
        switch ($action) {
            case 'chat':
                $this->handleChat($licenseData);
                break;
            case 'validate_key':
                $this->validateApiKey($licenseData);
                break;
            case 'test_connection':
                $this->testConnection($licenseData);
                break;
            default:
                $this->respondError('未知的操作类型', 400);
        }
    }
    
    /**
     * 处理PUT请求
     */
    private function handlePutRequest($action, $licenseData) {
        switch ($action) {
            case 'config':
                $this->updateConfig($licenseData);
                break;
            case 'add_api_key':
                $this->addApiKey($licenseData);
                break;
            default:
                $this->respondError('未知的操作类型', 400);
        }
    }
    
    /**
     * 处理DELETE请求
     */
    private function handleDeleteRequest($action, $licenseData) {
        switch ($action) {
            case 'api_key':
                $this->deleteApiKey($licenseData);
                break;
            default:
                $this->respondError('未知的操作类型', 400);
        }
    }
    
    /**
     * 验证license key
     */
    private function validateLicenseKey($licenseKey) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM license_keys 
                WHERE key_value = ? AND status = 'active' AND expiry_date > NOW()
            ");
            $stmt->execute([$licenseKey]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("License key validation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取DeepSeek配置
     */
    private function getConfig($licenseData) {
        try {
            // 获取配置
            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');
            
            // 获取API密钥（脱敏）
            $apiKeys = $this->getApiKeysForConfig($config['id'], true);
            
            $response = [
                'config' => [
                    'enabled' => (bool)$config['enabled'],
                    'model' => $config['model'],
                    'thinking_enabled' => (bool)$config['thinking_enabled'],
                    'reply_delay' => (int)$config['reply_delay'],
                    'system_prompt' => $config['system_prompt'],
                    'current_api_key_index' => (int)$config['current_api_key_index']
                ],
                'api_keys' => $apiKeys,
                'license_info' => [
                    'key_value' => $licenseData['key_value'],
                    'store_name' => $licenseData['store_name'],
                    'has_customer_service' => (bool)$licenseData['has_customer_service']
                ]
            ];
            
            $this->respondSuccess($response, 'DeepSeek配置获取成功');
            
        } catch (Exception $e) {
            error_log("Get DeepSeek config error: " . $e->getMessage());
            $this->respondError('获取配置失败', 500);
        }
    }
    
    /**
     * 获取DeepSeek状态
     */
    private function getStatus($licenseData) {
        try {
            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');
            
            // 检查是否启用
            if (!$config['enabled']) {
                $this->respondSuccess([
                    'enabled' => false,
                    'status' => 'disabled',
                    'message' => 'DeepSeek AI未启用'
                ]);
                return;
            }
            
            // 检查API密钥
            $validKeys = $this->getValidApiKeysCount($config['id']);
            if ($validKeys === 0) {
                $this->respondSuccess([
                    'enabled' => true,
                    'status' => 'no_valid_keys',
                    'message' => '没有有效的API密钥'
                ]);
                return;
            }
            
            $this->respondSuccess([
                'enabled' => true,
                'status' => 'ready',
                'message' => 'DeepSeek AI已就绪',
                'valid_api_keys' => $validKeys
            ]);
            
        } catch (Exception $e) {
            error_log("Get DeepSeek status error: " . $e->getMessage());
            $this->respondError('获取状态失败', 500);
        }
    }
    
    /**
     * 获取API密钥列表
     */
    private function getApiKeys($licenseData) {
        try {
            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');
            $apiKeys = $this->getApiKeysForConfig($config['id'], true);
            
            $this->respondSuccess([
                'api_keys' => $apiKeys,
                'total_count' => count($apiKeys),
                'valid_count' => array_sum(array_column($apiKeys, 'is_valid'))
            ], 'API密钥列表获取成功');
            
        } catch (Exception $e) {
            error_log("Get API keys error: " . $e->getMessage());
            $this->respondError('获取API密钥失败', 500);
        }
    }
    
    /**
     * 获取可用模型列表
     */
    private function getAvailableModels() {
        $models = [
            [
                'id' => 'deepseek-chat',
                'name' => 'DeepSeek Chat',
                'description' => '通用对话模型',
                'supports_thinking' => false
            ],
            [
                'id' => 'deepseek-r1-0528',
                'name' => 'DeepSeek-R1-0528',
                'description' => '支持深度思考的推理模型',
                'supports_thinking' => true
            ]
        ];
        
        $this->respondSuccess(['models' => $models], '模型列表获取成功');
    }
    
    /**
     * 获取API信息
     */
    private function getApiInfo() {
        $info = [
            'api_name' => 'DeepSeek AI客服API',
            'version' => '1.0.0',
            'service_type' => 'deepseek',
            'endpoints' => [
                'GET /config' => '获取DeepSeek配置',
                'GET /status' => '获取DeepSeek状态',
                'GET /api_keys' => '获取API密钥列表',
                'GET /models' => '获取可用模型列表',
                'POST /chat' => '发送聊天消息',
                'POST /validate_key' => '验证API密钥',
                'POST /test_connection' => '测试连接',
                'PUT /config' => '更新配置',
                'PUT /add_api_key' => '添加API密钥',
                'DELETE /api_key' => '删除API密钥'
            ],
            'required_params' => [
                'license_key' => '有效的授权密钥'
            ]
        ];
        
        $this->respondSuccess($info, 'DeepSeek API信息');
    }
    
    /**
     * 更新DeepSeek配置
     */
    private function updateConfig($licenseData) {
        try {
            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');
            
            // 验证输入数据
            $enabled = isset($this->request['enabled']) ? (bool)$this->request['enabled'] : $config['enabled'];
            $model = $this->request['model'] ?? $config['model'];
            $thinkingEnabled = isset($this->request['thinking_enabled']) ? (bool)$this->request['thinking_enabled'] : $config['thinking_enabled'];
            $replyDelay = isset($this->request['reply_delay']) ? (int)$this->request['reply_delay'] : $config['reply_delay'];
            $systemPrompt = $this->request['system_prompt'] ?? $config['system_prompt'];
            
            // 验证模型
            $validModels = ['deepseek-chat', 'deepseek-r1-0528'];
            if (!in_array($model, $validModels)) {
                $this->respondError('无效的模型', 400);
            }
            
            // 验证延迟时间
            if ($replyDelay < 0 || $replyDelay > 60) {
                $this->respondError('回复延迟必须在0-60秒之间', 400);
            }
            
            // 更新配置
            $stmt = $this->db->prepare("
                UPDATE ai_service_configs 
                SET enabled = ?, model = ?, thinking_enabled = ?, reply_delay = ?, system_prompt = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$enabled, $model, $thinkingEnabled, $replyDelay, $systemPrompt, $config['id']]);
            
            // 记录日志
            $this->logApiRequest('update_config', $licenseData['id'], 'success', 'DeepSeek配置更新成功');
            
            $this->respondSuccess([
                'config' => [
                    'enabled' => $enabled,
                    'model' => $model,
                    'thinking_enabled' => $thinkingEnabled,
                    'reply_delay' => $replyDelay,
                    'system_prompt' => $systemPrompt
                ]
            ], 'DeepSeek配置更新成功');
            
        } catch (Exception $e) {
            error_log("Update DeepSeek config error: " . $e->getMessage());
            $this->respondError('更新配置失败', 500);
        }
    }

    /**
     * 添加API密钥
     */
    private function addApiKey($licenseData) {
        try {
            $apiKey = $this->request['api_key'] ?? '';
            if (empty($apiKey)) {
                $this->respondError('API密钥不能为空', 400);
            }

            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');

            // 检查密钥是否已存在
            $keyHash = hash('sha256', $apiKey);
            $stmt = $this->db->prepare("
                SELECT id FROM ai_api_keys
                WHERE config_id = ? AND api_key_hash = ?
            ");
            $stmt->execute([$config['id'], $keyHash]);
            if ($stmt->fetch()) {
                $this->respondError('该API密钥已存在', 400);
            }

            // 验证API密钥
            $validation = $this->validateDeepSeekApiKey($apiKey);

            // 加密存储API密钥
            $encryptedKey = $this->encryptApiKey($apiKey);

            // 保存API密钥
            $stmt = $this->db->prepare("
                INSERT INTO ai_api_keys
                (config_id, api_key, api_key_hash, is_valid, last_validated_at, validation_error)
                VALUES (?, ?, ?, ?, NOW(), ?)
            ");
            $stmt->execute([
                $config['id'],
                $encryptedKey,
                $keyHash,
                $validation['valid'] ? 1 : 0,
                $validation['valid'] ? null : $validation['error']
            ]);

            $keyId = $this->db->lastInsertId();

            // 记录日志
            $this->logApiRequest('add_api_key', $licenseData['id'], 'success', 'API密钥添加成功');

            $this->respondSuccess([
                'key_id' => $keyId,
                'is_valid' => $validation['valid'],
                'validation_message' => $validation['valid'] ? 'API密钥验证成功' : $validation['error'],
                'masked_key' => $this->maskApiKey($apiKey)
            ], 'API密钥添加成功');

        } catch (Exception $e) {
            error_log("Add API key error: " . $e->getMessage());
            $this->respondError('添加API密钥失败', 500);
        }
    }

    /**
     * 删除API密钥
     */
    private function deleteApiKey($licenseData) {
        try {
            $keyId = $this->request['key_id'] ?? $_GET['key_id'] ?? '';
            if (empty($keyId)) {
                $this->respondError('缺少key_id参数', 400);
            }

            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');

            // 验证密钥是否属于当前配置
            $stmt = $this->db->prepare("
                SELECT id FROM ai_api_keys
                WHERE id = ? AND config_id = ?
            ");
            $stmt->execute([$keyId, $config['id']]);
            if (!$stmt->fetch()) {
                $this->respondError('API密钥不存在或无权限删除', 404);
            }

            // 删除API密钥
            $stmt = $this->db->prepare("DELETE FROM ai_api_keys WHERE id = ?");
            $stmt->execute([$keyId]);

            // 记录日志
            $this->logApiRequest('delete_api_key', $licenseData['id'], 'success', 'API密钥删除成功');

            $this->respondSuccess(['key_id' => $keyId], 'API密钥删除成功');

        } catch (Exception $e) {
            error_log("Delete API key error: " . $e->getMessage());
            $this->respondError('删除API密钥失败', 500);
        }
    }

    /**
     * 验证API密钥
     */
    private function validateApiKey($licenseData) {
        try {
            $keyId = $this->request['key_id'] ?? '';
            $apiKey = $this->request['api_key'] ?? '';

            if (empty($keyId) && empty($apiKey)) {
                $this->respondError('缺少key_id或api_key参数', 400);
            }

            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');

            // 如果提供了key_id，从数据库获取API密钥
            if (!empty($keyId)) {
                $stmt = $this->db->prepare("
                    SELECT api_key FROM ai_api_keys
                    WHERE id = ? AND config_id = ?
                ");
                $stmt->execute([$keyId, $config['id']]);
                $result = $stmt->fetch();
                if (!$result) {
                    $this->respondError('API密钥不存在', 404);
                }
                $apiKey = $this->decryptApiKey($result['api_key']);
            }

            // 验证API密钥
            $validation = $this->validateDeepSeekApiKey($apiKey);

            // 如果提供了key_id，更新数据库中的验证状态
            if (!empty($keyId)) {
                $stmt = $this->db->prepare("
                    UPDATE ai_api_keys
                    SET is_valid = ?, last_validated_at = NOW(), validation_error = ?
                    WHERE id = ?
                ");
                $stmt->execute([
                    $validation['valid'] ? 1 : 0,
                    $validation['valid'] ? null : $validation['error'],
                    $keyId
                ]);
            }

            // 记录日志
            $this->logApiRequest('validate_api_key', $licenseData['id'],
                $validation['valid'] ? 'success' : 'error',
                $validation['valid'] ? 'API密钥验证成功' : $validation['error']
            );

            $this->respondSuccess([
                'is_valid' => $validation['valid'],
                'message' => $validation['valid'] ? 'API密钥验证成功' : $validation['error'],
                'masked_key' => $this->maskApiKey($apiKey)
            ], $validation['valid'] ? 'API密钥验证成功' : 'API密钥验证失败');

        } catch (Exception $e) {
            error_log("Validate API key error: " . $e->getMessage());
            $this->respondError('验证API密钥失败', 500);
        }
    }

    /**
     * 测试连接
     */
    private function testConnection($licenseData) {
        try {
            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');

            // 检查是否启用
            if (!$config['enabled']) {
                $this->respondError('DeepSeek AI未启用', 400);
            }

            // 获取有效的API密钥
            $apiKey = $this->getValidApiKey($config['id']);
            if (!$apiKey) {
                $this->respondError('没有有效的API密钥', 400);
            }

            // 测试连接
            $testResult = $this->testDeepSeekConnection($apiKey, $config['model']);

            // 记录日志
            $this->logApiRequest('test_connection', $licenseData['id'],
                $testResult['success'] ? 'success' : 'error',
                $testResult['message']
            );

            if ($testResult['success']) {
                $this->respondSuccess([
                    'connection_status' => 'success',
                    'response_time' => $testResult['response_time'],
                    'model' => $config['model']
                ], 'DeepSeek连接测试成功');
            } else {
                $this->respondError('连接测试失败: ' . $testResult['message'], 500);
            }

        } catch (Exception $e) {
            error_log("Test connection error: " . $e->getMessage());
            $this->respondError('测试连接失败', 500);
        }
    }

    /**
     * 处理聊天请求
     */
    private function handleChat($licenseData) {
        try {
            $config = $this->getOrCreateConfig($licenseData['id'], 'deepseek');

            // 检查是否启用
            if (!$config['enabled']) {
                $this->respondError('DeepSeek AI未启用', 400);
            }

            // 获取有效的API密钥
            $apiKey = $this->getValidApiKey($config['id']);
            if (!$apiKey) {
                $this->respondError('没有有效的API密钥', 400);
            }

            // 验证输入参数
            $message = $this->request['message'] ?? '';
            $sessionId = $this->request['session_id'] ?? '';
            $context = $this->request['context'] ?? [];

            if (empty($message)) {
                $this->respondError('消息内容不能为空', 400);
            }

            // 构建聊天请求
            $chatRequest = [
                'model' => $config['model'],
                'messages' => [],
                'temperature' => 0.7,
                'max_tokens' => 1000
            ];

            // 添加系统提示词
            if (!empty($config['system_prompt'])) {
                $chatRequest['messages'][] = [
                    'role' => 'system',
                    'content' => $config['system_prompt']
                ];
            }

            // 添加上下文消息
            if (!empty($context) && is_array($context)) {
                foreach ($context as $msg) {
                    if (isset($msg['role']) && isset($msg['content'])) {
                        $chatRequest['messages'][] = [
                            'role' => $msg['role'],
                            'content' => $msg['content']
                        ];
                    }
                }
            }

            // 添加当前消息
            $chatRequest['messages'][] = [
                'role' => 'user',
                'content' => $message
            ];

            // 发送请求到DeepSeek API
            $startTime = microtime(true);
            $response = $this->sendDeepSeekRequest($apiKey, $chatRequest);
            $responseTime = round((microtime(true) - $startTime) * 1000);

            if ($response['success']) {
                // 记录成功日志
                $this->logApiRequest('chat', $licenseData['id'], 'success', 'DeepSeek聊天成功');

                $this->respondSuccess([
                    'response' => $response['data']['choices'][0]['message']['content'],
                    'model' => $config['model'],
                    'session_id' => $sessionId,
                    'response_time' => $responseTime,
                    'usage' => $response['data']['usage'] ?? null
                ], 'DeepSeek聊天成功');
            } else {
                // 记录错误日志
                $this->logApiRequest('chat', $licenseData['id'], 'error', $response['error']);

                $this->respondError('DeepSeek聊天失败: ' . $response['error'], 500);
            }

        } catch (Exception $e) {
            error_log("DeepSeek chat error: " . $e->getMessage());
            $this->respondError('聊天请求失败', 500);
        }
    }

    /**
     * 获取或创建AI配置
     */
    private function getOrCreateConfig($licenseKeyId, $serviceType) {
        try {
            // 尝试获取现有配置
            $stmt = $this->db->prepare("
                SELECT * FROM ai_service_configs
                WHERE license_key_id = ? AND service_type = ?
            ");
            $stmt->execute([$licenseKeyId, $serviceType]);
            $config = $stmt->fetch();

            if ($config) {
                return $config;
            }

            // 创建默认配置
            $defaultPrompt = '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。';
            $defaultModel = $serviceType === 'deepseek' ? 'deepseek-chat' : 'doubao-seed-1-6-250615';

            $stmt = $this->db->prepare("
                INSERT INTO ai_service_configs
                (license_key_id, service_type, enabled, model, thinking_enabled, reply_delay, system_prompt)
                VALUES (?, ?, 0, ?, 0, 0, ?)
            ");
            $stmt->execute([$licenseKeyId, $serviceType, $defaultModel, $defaultPrompt]);

            $configId = $this->db->lastInsertId();

            // 返回新创建的配置
            $stmt = $this->db->prepare("SELECT * FROM ai_service_configs WHERE id = ?");
            $stmt->execute([$configId]);
            return $stmt->fetch();

        } catch (PDOException $e) {
            error_log("Get or create config error: " . $e->getMessage());
            throw new Exception('配置获取失败');
        }
    }

    /**
     * 获取配置的API密钥列表
     */
    private function getApiKeysForConfig($configId, $masked = true) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, api_key, is_valid, last_validated_at, validation_error,
                       usage_count, last_used_at, created_at
                FROM ai_api_keys
                WHERE config_id = ?
                ORDER BY created_at DESC
            ");
            $stmt->execute([$configId]);
            $keys = $stmt->fetchAll();

            $result = [];
            foreach ($keys as $key) {
                $apiKey = $this->decryptApiKey($key['api_key']);
                $result[] = [
                    'id' => $key['id'],
                    'api_key' => $masked ? $this->maskApiKey($apiKey) : $apiKey,
                    'is_valid' => (bool)$key['is_valid'],
                    'last_validated_at' => $key['last_validated_at'],
                    'validation_error' => $key['validation_error'],
                    'usage_count' => (int)$key['usage_count'],
                    'last_used_at' => $key['last_used_at'],
                    'created_at' => $key['created_at']
                ];
            }

            return $result;

        } catch (PDOException $e) {
            error_log("Get API keys error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取有效API密钥数量
     */
    private function getValidApiKeysCount($configId) {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM ai_api_keys
                WHERE config_id = ? AND is_valid = 1
            ");
            $stmt->execute([$configId]);
            return (int)$stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Get valid API keys count error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取有效的API密钥
     */
    private function getValidApiKey($configId) {
        try {
            $stmt = $this->db->prepare("
                SELECT api_key FROM ai_api_keys
                WHERE config_id = ? AND is_valid = 1
                ORDER BY last_used_at ASC, id ASC
                LIMIT 1
            ");
            $stmt->execute([$configId]);
            $result = $stmt->fetch();

            if ($result) {
                return $this->decryptApiKey($result['api_key']);
            }

            return null;
        } catch (PDOException $e) {
            error_log("Get valid API key error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 验证DeepSeek API密钥
     */
    private function validateDeepSeekApiKey($apiKey) {
        try {
            $url = 'https://api.deepseek.com/v1/chat/completions';
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ];

            $data = [
                'model' => 'deepseek-chat',
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello']
                ],
                'max_tokens' => 10
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return ['valid' => false, 'error' => 'cURL错误: ' . $error];
            }

            if ($httpCode === 200) {
                $responseData = json_decode($response, true);
                if (isset($responseData['choices'])) {
                    return ['valid' => true, 'error' => null];
                }
            }

            $responseData = json_decode($response, true);
            $errorMsg = $responseData['error']['message'] ?? 'API密钥验证失败';

            return ['valid' => false, 'error' => $errorMsg];

        } catch (Exception $e) {
            return ['valid' => false, 'error' => '验证过程异常: ' . $e->getMessage()];
        }
    }

    /**
     * 测试DeepSeek连接
     */
    private function testDeepSeekConnection($apiKey, $model) {
        try {
            $startTime = microtime(true);

            $url = 'https://api.deepseek.com/v1/chat/completions';
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ];

            $data = [
                'model' => $model,
                'messages' => [
                    ['role' => 'user', 'content' => '测试连接']
                ],
                'max_tokens' => 10
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            $responseTime = round((microtime(true) - $startTime) * 1000);

            if ($error) {
                return ['success' => false, 'message' => 'cURL错误: ' . $error, 'response_time' => $responseTime];
            }

            if ($httpCode === 200) {
                return ['success' => true, 'message' => '连接测试成功', 'response_time' => $responseTime];
            }

            $responseData = json_decode($response, true);
            $errorMsg = $responseData['error']['message'] ?? '连接测试失败';

            return ['success' => false, 'message' => $errorMsg, 'response_time' => $responseTime];

        } catch (Exception $e) {
            return ['success' => false, 'message' => '测试过程异常: ' . $e->getMessage(), 'response_time' => 0];
        }
    }

    /**
     * 发送DeepSeek请求
     */
    private function sendDeepSeekRequest($apiKey, $requestData) {
        try {
            $url = 'https://api.deepseek.com/v1/chat/completions';
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return ['success' => false, 'error' => 'cURL错误: ' . $error];
            }

            $responseData = json_decode($response, true);

            if ($httpCode === 200 && isset($responseData['choices'])) {
                return ['success' => true, 'data' => $responseData];
            }

            $errorMsg = $responseData['error']['message'] ?? '请求失败';
            return ['success' => false, 'error' => $errorMsg];

        } catch (Exception $e) {
            return ['success' => false, 'error' => '请求过程异常: ' . $e->getMessage()];
        }
    }

    /**
     * 获取加密密钥
     */
    private function getEncryptionKey() {
        try {
            $stmt = $this->db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'api_secret_key'");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result ? $result['setting_value'] : 'default_encryption_key_change_this';
        } catch (PDOException $e) {
            error_log("Get encryption key error: " . $e->getMessage());
            return 'default_encryption_key_change_this';
        }
    }

    /**
     * 加密API密钥
     */
    private function encryptApiKey($apiKey) {
        $method = 'AES-256-CBC';
        $key = hash('sha256', $this->encryptionKey);
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($apiKey, $method, $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }

    /**
     * 解密API密钥
     */
    private function decryptApiKey($encryptedApiKey) {
        $method = 'AES-256-CBC';
        $key = hash('sha256', $this->encryptionKey);
        $data = base64_decode($encryptedApiKey);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, $method, $key, 0, $iv);
    }

    /**
     * 脱敏API密钥
     */
    private function maskApiKey($apiKey) {
        if (strlen($apiKey) <= 16) {
            return str_repeat('*', strlen($apiKey));
        }
        return substr($apiKey, 0, 8) . '...' . substr($apiKey, -8);
    }

    /**
     * 记录API请求日志
     */
    protected function logApiRequest($endpoint, $licenseKeyId = null, $status = 'success', $message = '') {
        try {
            // 获取配置ID
            $configId = null;
            if ($licenseKeyId) {
                $stmt = $this->db->prepare("
                    SELECT id FROM ai_service_configs
                    WHERE license_key_id = ? AND service_type = 'deepseek'
                ");
                $stmt->execute([$licenseKeyId]);
                $result = $stmt->fetch();
                $configId = $result ? $result['id'] : null;
            }

            $stmt = $this->db->prepare("
                INSERT INTO ai_service_logs
                (config_id, action, request_data, status, error_message, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            // 过滤敏感信息
            $filteredRequest = $this->request;
            if (isset($filteredRequest['api_key'])) {
                $filteredRequest['api_key'] = $this->maskApiKey($filteredRequest['api_key']);
            }

            $stmt->execute([
                $configId,
                $endpoint,
                json_encode($filteredRequest),
                $status,
                $status === 'error' ? $message : null,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            error_log("Log API request error: " . $e->getMessage());
        }
    }
}

// 创建API实例并处理请求
new DeepSeekAPI();
