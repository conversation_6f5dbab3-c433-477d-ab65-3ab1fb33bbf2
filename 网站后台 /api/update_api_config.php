<?php
/**
 * API配置更新脚本
 * 用于将现有的DeepSeek和豆包API配置更新到最新版本
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入最新配置
define('API_ACCESS', true);
require_once __DIR__ . '/api_config_latest.php';

/**
 * 响应成功
 */
function respondSuccess($data = [], $message = 'Success') {
    $response = [
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

/**
 * 响应错误
 */
function respondError($message = 'Error', $code = 400) {
    $response = [
        'success' => false,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    http_response_code($code);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

/**
 * 连接数据库
 */
function connectDatabase() {
    try {
        $dbConfig = [
            'host' => '127.0.0.1',
            'port' => '3306',
            'dbname' => 'xiaomeihuakefu_c',
            'user' => 'xiaomeihuakefu_c',
            'pass' => '7Da5F1Xx995cxYz8'
        ];
        
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
        
        return $pdo;
    } catch (PDOException $e) {
        respondError('数据库连接失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 更新DeepSeek配置
 */
function updateDeepSeekConfig($db) {
    try {
        $updates = [];
        
        // 检查是否有DeepSeek配置需要更新
        $stmt = $db->prepare("
            SELECT id, model FROM ai_service_configs 
            WHERE service_type = 'deepseek'
        ");
        $stmt->execute();
        $configs = $stmt->fetchAll();
        
        foreach ($configs as $config) {
            $configId = $config['id'];
            $currentModel = $config['model'];
            
            // 如果使用的是旧模型名称，更新为新的
            $newModel = $currentModel;
            if ($currentModel === 'deepseek-r1-0528') {
                $newModel = 'deepseek-reasoner';
            }
            
            if ($newModel !== $currentModel) {
                $stmt = $db->prepare("
                    UPDATE ai_service_configs 
                    SET model = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$newModel, $configId]);
                
                $updates[] = [
                    'config_id' => $configId,
                    'old_model' => $currentModel,
                    'new_model' => $newModel
                ];
            }
        }
        
        return $updates;
    } catch (PDOException $e) {
        throw new Exception('更新DeepSeek配置失败: ' . $e->getMessage());
    }
}

/**
 * 更新豆包配置
 */
function updateDoubaoConfig($db) {
    try {
        $updates = [];
        
        // 检查是否有豆包配置需要更新
        $stmt = $db->prepare("
            SELECT id, model FROM ai_service_configs 
            WHERE service_type = 'doubao'
        ");
        $stmt->execute();
        $configs = $stmt->fetchAll();
        
        foreach ($configs as $config) {
            $configId = $config['id'];
            $currentModel = $config['model'];
            
            // 验证当前模型是否在支持列表中
            if (!LatestApiConfig::isValidDoubaoModel($currentModel)) {
                // 如果模型不在支持列表中，更新为默认模型
                $newModel = LatestApiConfig::getDoubaoConfig()['default_model'];
                
                $stmt = $db->prepare("
                    UPDATE ai_service_configs 
                    SET model = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$newModel, $configId]);
                
                $updates[] = [
                    'config_id' => $configId,
                    'old_model' => $currentModel,
                    'new_model' => $newModel,
                    'reason' => '模型不在支持列表中，已更新为默认模型'
                ];
            }
        }
        
        return $updates;
    } catch (PDOException $e) {
        throw new Exception('更新豆包配置失败: ' . $e->getMessage());
    }
}

/**
 * 检查API文件版本
 */
function checkApiFileVersions() {
    $files = [
        'deepseek_api.php' => __DIR__ . '/deepseek_api.php',
        'doubao_api.php' => __DIR__ . '/doubao_api.php',
        'deepseek_api_fixed.php' => __DIR__ . '/deepseek_api_fixed.php',
        'doubao_api_fixed.php' => __DIR__ . '/doubao_api_fixed.php'
    ];
    
    $fileStatus = [];
    
    foreach ($files as $name => $path) {
        if (file_exists($path)) {
            $content = file_get_contents($path);
            
            // 检查是否使用了最新的API地址
            $hasLatestDeepSeekUrl = strpos($content, 'https://api.deepseek.com/chat/completions') !== false;
            $hasLatestDoubaoUrl = strpos($content, 'https://ark.cn-beijing.volces.com/api/v3/chat/completions') !== false;
            
            $fileStatus[$name] = [
                'exists' => true,
                'has_latest_deepseek_url' => $hasLatestDeepSeekUrl,
                'has_latest_doubao_url' => $hasLatestDoubaoUrl,
                'needs_update' => false
            ];
            
            // 判断是否需要更新
            if (strpos($name, 'deepseek') !== false && !$hasLatestDeepSeekUrl) {
                $fileStatus[$name]['needs_update'] = true;
            }
            if (strpos($name, 'doubao') !== false && !$hasLatestDoubaoUrl) {
                $fileStatus[$name]['needs_update'] = true;
            }
        } else {
            $fileStatus[$name] = [
                'exists' => false,
                'needs_update' => true
            ];
        }
    }
    
    return $fileStatus;
}

// 处理请求
$action = $_GET['action'] ?? $_POST['action'] ?? 'info';

switch ($action) {
    case 'info':
        respondSuccess([
            'api_name' => 'API配置更新工具',
            'version' => '1.0.0',
            'latest_config_version' => '2.0.0',
            'available_actions' => [
                'info' => '获取更新工具信息',
                'check' => '检查当前配置状态',
                'update_db' => '更新数据库中的API配置',
                'check_files' => '检查API文件版本',
                'get_latest_config' => '获取最新配置信息'
            ],
            'deepseek_latest' => LatestApiConfig::getDeepSeekConfig(),
            'doubao_latest' => LatestApiConfig::getDoubaoConfig()
        ], 'API配置更新工具');
        break;
        
    case 'check':
        $db = connectDatabase();
        
        // 检查数据库配置
        $stmt = $db->query("
            SELECT service_type, COUNT(*) as count, 
                   GROUP_CONCAT(DISTINCT model) as models
            FROM ai_service_configs 
            GROUP BY service_type
        ");
        $dbConfigs = $stmt->fetchAll();
        
        // 检查文件版本
        $fileStatus = checkApiFileVersions();
        
        respondSuccess([
            'database_configs' => $dbConfigs,
            'file_status' => $fileStatus,
            'latest_config_available' => true
        ], '配置检查完成');
        break;
        
    case 'update_db':
        $db = connectDatabase();
        
        try {
            $db->beginTransaction();
            
            $deepseekUpdates = updateDeepSeekConfig($db);
            $doubaoUpdates = updateDoubaoConfig($db);
            
            $db->commit();
            
            respondSuccess([
                'deepseek_updates' => $deepseekUpdates,
                'doubao_updates' => $doubaoUpdates,
                'total_updates' => count($deepseekUpdates) + count($doubaoUpdates)
            ], '数据库配置更新完成');
            
        } catch (Exception $e) {
            $db->rollBack();
            respondError('更新失败: ' . $e->getMessage(), 500);
        }
        break;
        
    case 'check_files':
        $fileStatus = checkApiFileVersions();
        respondSuccess($fileStatus, '文件版本检查完成');
        break;
        
    case 'get_latest_config':
        respondSuccess(LatestApiConfig::getAllConfigs(), '最新配置信息');
        break;
        
    default:
        respondError('未知的操作类型: ' . $action, 400);
}
?>
