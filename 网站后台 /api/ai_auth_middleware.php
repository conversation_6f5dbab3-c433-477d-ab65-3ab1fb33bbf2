<?php
/**
 * AI API权限验证中间件
 * 为DeepSeek和豆包API提供统一的权限验证功能
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'Access denied']);
    exit;
}

class AIAuthMiddleware {
    
    private $db;
    private $config;
    
    public function __construct($db, $config) {
        $this->db = $db;
        $this->config = $config;
    }
    
    /**
     * 验证API访问权限
     * 
     * @param string $licenseKey 授权密钥
     * @param string $serviceType AI服务类型 (deepseek/doubao)
     * @param string $action 操作类型
     * @return array 验证结果
     */
    public function validateAccess($licenseKey, $serviceType, $action = '') {
        try {
            // 1. 验证license key基本有效性
            $licenseData = $this->validateLicenseKey($licenseKey);
            if (!$licenseData['valid']) {
                return [
                    'valid' => false,
                    'error' => $licenseData['error'],
                    'error_code' => 'INVALID_LICENSE'
                ];
            }
            
            // 2. 检查AI客服功能权限
            $permissionCheck = $this->checkAIPermission($licenseData['data'], $serviceType);
            if (!$permissionCheck['valid']) {
                return [
                    'valid' => false,
                    'error' => $permissionCheck['error'],
                    'error_code' => 'NO_PERMISSION'
                ];
            }
            
            // 3. 检查服务启用状态（仅对需要启用的操作）
            if ($this->requiresServiceEnabled($action)) {
                $enabledCheck = $this->checkServiceEnabled($licenseData['data']['id'], $serviceType);
                if (!$enabledCheck['valid']) {
                    return [
                        'valid' => false,
                        'error' => $enabledCheck['error'],
                        'error_code' => 'SERVICE_DISABLED'
                    ];
                }
            }
            
            // 4. 检查API密钥（仅对需要API密钥的操作）
            if ($this->requiresValidApiKey($action)) {
                $apiKeyCheck = $this->checkValidApiKey($licenseData['data']['id'], $serviceType);
                if (!$apiKeyCheck['valid']) {
                    return [
                        'valid' => false,
                        'error' => $apiKeyCheck['error'],
                        'error_code' => 'NO_VALID_API_KEY'
                    ];
                }
            }
            
            // 5. 检查速率限制
            $rateLimitCheck = $this->checkRateLimit($licenseData['data']['id'], $serviceType, $action);
            if (!$rateLimitCheck['valid']) {
                return [
                    'valid' => false,
                    'error' => $rateLimitCheck['error'],
                    'error_code' => 'RATE_LIMIT_EXCEEDED'
                ];
            }
            
            return [
                'valid' => true,
                'license_data' => $licenseData['data'],
                'config_data' => $enabledCheck['config'] ?? null
            ];
            
        } catch (Exception $e) {
            error_log("AI Auth validation error: " . $e->getMessage());
            return [
                'valid' => false,
                'error' => '权限验证失败',
                'error_code' => 'VALIDATION_ERROR'
            ];
        }
    }
    
    /**
     * 验证license key
     */
    private function validateLicenseKey($licenseKey) {
        try {
            if (empty($licenseKey)) {
                return ['valid' => false, 'error' => '缺少授权密钥'];
            }
            
            $stmt = $this->db->prepare("
                SELECT * FROM license_keys 
                WHERE key_value = ? AND status = 'active'
            ");
            $stmt->execute([$licenseKey]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                return ['valid' => false, 'error' => '无效的授权密钥'];
            }
            
            // 检查过期时间
            if ($keyData['expiry_date'] && strtotime($keyData['expiry_date']) < time()) {
                return ['valid' => false, 'error' => '授权密钥已过期'];
            }
            
            // 更新最后使用时间
            $this->updateLastUsed($keyData['id']);
            
            return ['valid' => true, 'data' => $keyData];
            
        } catch (PDOException $e) {
            error_log("License key validation error: " . $e->getMessage());
            return ['valid' => false, 'error' => '授权验证失败'];
        }
    }
    
    /**
     * 检查AI客服功能权限
     */
    private function checkAIPermission($licenseData, $serviceType) {
        // 检查是否有AI客服功能权限
        if (!$licenseData['has_customer_service']) {
            return [
                'valid' => false,
                'error' => '该授权密钥不包含AI客服功能'
            ];
        }
        
        // 可以根据不同的服务类型添加更细粒度的权限检查
        // 目前DeepSeek和豆包都使用相同的客服权限
        
        return ['valid' => true];
    }
    
    /**
     * 检查服务启用状态
     */
    private function checkServiceEnabled($licenseKeyId, $serviceType) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM ai_service_configs 
                WHERE license_key_id = ? AND service_type = ?
            ");
            $stmt->execute([$licenseKeyId, $serviceType]);
            $config = $stmt->fetch();
            
            if (!$config) {
                return [
                    'valid' => false,
                    'error' => ucfirst($serviceType) . ' AI服务未配置'
                ];
            }
            
            if (!$config['enabled']) {
                return [
                    'valid' => false,
                    'error' => ucfirst($serviceType) . ' AI服务未启用'
                ];
            }
            
            return ['valid' => true, 'config' => $config];
            
        } catch (PDOException $e) {
            error_log("Service enabled check error: " . $e->getMessage());
            return ['valid' => false, 'error' => '服务状态检查失败'];
        }
    }
    
    /**
     * 检查有效的API密钥
     */
    private function checkValidApiKey($licenseKeyId, $serviceType) {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM ai_service_configs asc
                JOIN ai_api_keys aak ON asc.id = aak.config_id
                WHERE asc.license_key_id = ? AND asc.service_type = ? AND aak.is_valid = 1
            ");
            $stmt->execute([$licenseKeyId, $serviceType]);
            $validKeyCount = $stmt->fetchColumn();
            
            if ($validKeyCount == 0) {
                return [
                    'valid' => false,
                    'error' => ucfirst($serviceType) . ' 没有有效的API密钥'
                ];
            }
            
            return ['valid' => true];
            
        } catch (PDOException $e) {
            error_log("API key check error: " . $e->getMessage());
            return ['valid' => false, 'error' => 'API密钥检查失败'];
        }
    }
    
    /**
     * 检查速率限制
     */
    private function checkRateLimit($licenseKeyId, $serviceType, $action) {
        try {
            // 获取速率限制配置
            $limits = $this->getRateLimits($serviceType, $action);
            
            if (empty($limits)) {
                return ['valid' => true]; // 没有配置限制
            }
            
            // 检查每分钟请求限制
            if (isset($limits['requests_per_minute'])) {
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) FROM ai_service_logs asl
                    JOIN ai_service_configs asc ON asl.config_id = asc.id
                    WHERE asc.license_key_id = ? AND asc.service_type = ? 
                    AND asl.action = ? AND asl.created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
                ");
                $stmt->execute([$licenseKeyId, $serviceType, $action]);
                $recentRequests = $stmt->fetchColumn();
                
                if ($recentRequests >= $limits['requests_per_minute']) {
                    return [
                        'valid' => false,
                        'error' => '请求频率过高，请稍后再试'
                    ];
                }
            }
            
            // 检查每小时请求限制
            if (isset($limits['requests_per_hour'])) {
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) FROM ai_service_logs asl
                    JOIN ai_service_configs asc ON asl.config_id = asc.id
                    WHERE asc.license_key_id = ? AND asc.service_type = ? 
                    AND asl.action = ? AND asl.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ");
                $stmt->execute([$licenseKeyId, $serviceType, $action]);
                $recentRequests = $stmt->fetchColumn();
                
                if ($recentRequests >= $limits['requests_per_hour']) {
                    return [
                        'valid' => false,
                        'error' => '小时请求限制已达上限'
                    ];
                }
            }
            
            return ['valid' => true];
            
        } catch (PDOException $e) {
            error_log("Rate limit check error: " . $e->getMessage());
            return ['valid' => true]; // 出错时不阻止请求
        }
    }
    
    /**
     * 获取速率限制配置
     */
    private function getRateLimits($serviceType, $action) {
        // 默认速率限制配置
        $defaultLimits = [
            'chat' => [
                'requests_per_minute' => 60,
                'requests_per_hour' => 1000
            ],
            'validate_key' => [
                'requests_per_minute' => 10,
                'requests_per_hour' => 100
            ],
            'test_connection' => [
                'requests_per_minute' => 5,
                'requests_per_hour' => 50
            ]
        ];
        
        return $defaultLimits[$action] ?? [];
    }
    
    /**
     * 判断操作是否需要服务启用
     */
    private function requiresServiceEnabled($action) {
        $enabledRequiredActions = ['chat', 'test_connection'];
        return in_array($action, $enabledRequiredActions);
    }
    
    /**
     * 判断操作是否需要有效的API密钥
     */
    private function requiresValidApiKey($action) {
        $apiKeyRequiredActions = ['chat', 'test_connection', 'validate_key'];
        return in_array($action, $apiKeyRequiredActions);
    }
    
    /**
     * 更新最后使用时间
     */
    private function updateLastUsed($licenseKeyId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE license_keys 
                SET last_used_at = NOW(), last_ip = ?, last_user_agent = ?
                WHERE id = ?
            ");
            $stmt->execute([
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $licenseKeyId
            ]);
        } catch (PDOException $e) {
            error_log("Update last used error: " . $e->getMessage());
        }
    }
    
    /**
     * 记录访问日志
     */
    public function logAccess($licenseKeyId, $serviceType, $action, $result, $message = '') {
        try {
            // 获取配置ID
            $configId = null;
            if ($licenseKeyId) {
                $stmt = $this->db->prepare("
                    SELECT id FROM ai_service_configs 
                    WHERE license_key_id = ? AND service_type = ?
                ");
                $stmt->execute([$licenseKeyId, $serviceType]);
                $config = $stmt->fetch();
                $configId = $config ? $config['id'] : null;
            }
            
            $stmt = $this->db->prepare("
                INSERT INTO ai_service_logs 
                (config_id, action, status, error_message, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $configId,
                $action,
                $result ? 'success' : 'error',
                $result ? null : $message,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            error_log("Log access error: " . $e->getMessage());
        }
    }
}
