<?php
/**
 * AI服务管理API
 * 提供统一的AI服务管理功能，包括配置同步、状态监控等
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// 定义API访问常量
define('API_ACCESS', true);

// 引入基础类和配置
require_once __DIR__ . '/api_base.php';
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/ai_auth_middleware.php';

class AIServiceManager extends ApiBase {
    
    private $authMiddleware;
    
    public function __construct() {
        parent::__construct();
        
        // 初始化权限验证中间件
        $this->authMiddleware = new AIAuthMiddleware($this->db, $this->config);
        
        // 处理请求
        $this->handleRequest();
    }
    
    /**
     * 处理API请求
     */
    private function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        // 验证卡密
        $licenseKey = $this->request['license_key'] ?? $_GET['license_key'] ?? '';
        if (empty($licenseKey)) {
            $this->respondError('缺少license_key参数', 400);
        }
        
        // 路由处理
        switch ($method) {
            case 'GET':
                $this->handleGetRequest($action, $licenseKey);
                break;
            case 'POST':
                $this->handlePostRequest($action, $licenseKey);
                break;
            case 'PUT':
                $this->handlePutRequest($action, $licenseKey);
                break;
            default:
                $this->respondError('不支持的请求方法', 405);
        }
    }
    
    /**
     * 处理GET请求
     */
    private function handleGetRequest($action, $licenseKey) {
        switch ($action) {
            case 'status':
                $this->getAllServicesStatus($licenseKey);
                break;
            case 'configs':
                $this->getAllConfigs($licenseKey);
                break;
            case 'stats':
                $this->getUsageStats($licenseKey);
                break;
            case 'info':
                $this->getManagerInfo();
                break;
            default:
                $this->respondError('未知的操作类型', 400);
        }
    }
    
    /**
     * 处理POST请求
     */
    private function handlePostRequest($action, $licenseKey) {
        switch ($action) {
            case 'sync_from_frontend':
                $this->syncFromFrontend($licenseKey);
                break;
            case 'validate_all_keys':
                $this->validateAllApiKeys($licenseKey);
                break;
            case 'reset_configs':
                $this->resetConfigs($licenseKey);
                break;
            default:
                $this->respondError('未知的操作类型', 400);
        }
    }
    
    /**
     * 处理PUT请求
     */
    private function handlePutRequest($action, $licenseKey) {
        switch ($action) {
            case 'batch_update':
                $this->batchUpdateConfigs($licenseKey);
                break;
            default:
                $this->respondError('未知的操作类型', 400);
        }
    }
    
    /**
     * 获取所有AI服务状态
     */
    private function getAllServicesStatus($licenseKey) {
        try {
            // 验证权限（不需要服务启用）
            $authResult = $this->authMiddleware->validateAccess($licenseKey, 'deepseek', 'status');
            if (!$authResult['valid']) {
                $this->respondError($authResult['error'], 403, $authResult['error_code']);
            }
            
            $licenseData = $authResult['license_data'];
            
            // 获取DeepSeek状态
            $deepseekStatus = $this->getServiceStatus($licenseData['id'], 'deepseek');
            
            // 获取豆包状态
            $doubaoStatus = $this->getServiceStatus($licenseData['id'], 'doubao');
            
            $response = [
                'license_info' => [
                    'key_value' => $licenseData['key_value'],
                    'store_name' => $licenseData['store_name'],
                    'has_customer_service' => (bool)$licenseData['has_customer_service'],
                    'expiry_date' => $licenseData['expiry_date']
                ],
                'services' => [
                    'deepseek' => $deepseekStatus,
                    'doubao' => $doubaoStatus
                ],
                'overall_status' => $this->calculateOverallStatus($deepseekStatus, $doubaoStatus)
            ];
            
            $this->respondSuccess($response, 'AI服务状态获取成功');
            
        } catch (Exception $e) {
            error_log("Get all services status error: " . $e->getMessage());
            $this->respondError('获取服务状态失败', 500);
        }
    }
    
    /**
     * 获取所有配置
     */
    private function getAllConfigs($licenseKey) {
        try {
            // 验证权限
            $authResult = $this->authMiddleware->validateAccess($licenseKey, 'deepseek', 'config');
            if (!$authResult['valid']) {
                $this->respondError($authResult['error'], 403, $authResult['error_code']);
            }
            
            $licenseData = $authResult['license_data'];
            
            // 获取DeepSeek配置
            $deepseekConfig = $this->getServiceConfig($licenseData['id'], 'deepseek');
            
            // 获取豆包配置
            $doubaoConfig = $this->getServiceConfig($licenseData['id'], 'doubao');
            
            $response = [
                'license_info' => [
                    'key_value' => $licenseData['key_value'],
                    'store_name' => $licenseData['store_name']
                ],
                'configs' => [
                    'deepseek' => $deepseekConfig,
                    'doubao' => $doubaoConfig
                ]
            ];
            
            $this->respondSuccess($response, 'AI服务配置获取成功');
            
        } catch (Exception $e) {
            error_log("Get all configs error: " . $e->getMessage());
            $this->respondError('获取配置失败', 500);
        }
    }
    
    /**
     * 获取使用统计
     */
    private function getUsageStats($licenseKey) {
        try {
            // 验证权限
            $authResult = $this->authMiddleware->validateAccess($licenseKey, 'deepseek', 'stats');
            if (!$authResult['valid']) {
                $this->respondError($authResult['error'], 403, $authResult['error_code']);
            }
            
            $licenseData = $authResult['license_data'];
            
            // 获取统计数据
            $stats = $this->calculateUsageStats($licenseData['id']);
            
            $this->respondSuccess($stats, '使用统计获取成功');
            
        } catch (Exception $e) {
            error_log("Get usage stats error: " . $e->getMessage());
            $this->respondError('获取统计失败', 500);
        }
    }
    
    /**
     * 从前端同步配置
     */
    private function syncFromFrontend($licenseKey) {
        try {
            // 验证权限
            $authResult = $this->authMiddleware->validateAccess($licenseKey, 'deepseek', 'sync');
            if (!$authResult['valid']) {
                $this->respondError($authResult['error'], 403, $authResult['error_code']);
            }
            
            $licenseData = $authResult['license_data'];
            
            // 获取前端配置数据
            $frontendData = $this->request['ai_settings'] ?? [];
            if (empty($frontendData)) {
                $this->respondError('缺少ai_settings数据', 400);
            }
            
            $syncResults = [];
            
            // 同步DeepSeek配置
            if (isset($frontendData['deepseek'])) {
                $syncResults['deepseek'] = $this->syncServiceConfig(
                    $licenseData['id'], 
                    'deepseek', 
                    $frontendData['deepseek']
                );
            }
            
            // 同步豆包配置
            if (isset($frontendData['doubao'])) {
                $syncResults['doubao'] = $this->syncServiceConfig(
                    $licenseData['id'], 
                    'doubao', 
                    $frontendData['doubao']
                );
            }
            
            $this->respondSuccess([
                'sync_results' => $syncResults,
                'synced_at' => date('Y-m-d H:i:s')
            ], '配置同步成功');
            
        } catch (Exception $e) {
            error_log("Sync from frontend error: " . $e->getMessage());
            $this->respondError('配置同步失败', 500);
        }
    }
    
    /**
     * 验证所有API密钥
     */
    private function validateAllApiKeys($licenseKey) {
        try {
            // 验证权限
            $authResult = $this->authMiddleware->validateAccess($licenseKey, 'deepseek', 'validate_all');
            if (!$authResult['valid']) {
                $this->respondError($authResult['error'], 403, $authResult['error_code']);
            }
            
            $licenseData = $authResult['license_data'];
            
            $validationResults = [];
            
            // 验证DeepSeek API密钥
            $validationResults['deepseek'] = $this->validateServiceApiKeys($licenseData['id'], 'deepseek');
            
            // 验证豆包API密钥
            $validationResults['doubao'] = $this->validateServiceApiKeys($licenseData['id'], 'doubao');
            
            $this->respondSuccess([
                'validation_results' => $validationResults,
                'validated_at' => date('Y-m-d H:i:s')
            ], 'API密钥验证完成');
            
        } catch (Exception $e) {
            error_log("Validate all API keys error: " . $e->getMessage());
            $this->respondError('API密钥验证失败', 500);
        }
    }
    
    /**
     * 获取管理器信息
     */
    private function getManagerInfo() {
        $info = [
            'api_name' => 'AI服务管理API',
            'version' => '1.0.0',
            'supported_services' => ['deepseek', 'doubao'],
            'endpoints' => [
                'GET /status' => '获取所有AI服务状态',
                'GET /configs' => '获取所有AI服务配置',
                'GET /stats' => '获取使用统计',
                'POST /sync_from_frontend' => '从前端同步配置',
                'POST /validate_all_keys' => '验证所有API密钥',
                'POST /reset_configs' => '重置配置',
                'PUT /batch_update' => '批量更新配置'
            ],
            'required_params' => [
                'license_key' => '有效的授权密钥'
            ]
        ];
        
        $this->respondSuccess($info, 'AI服务管理器信息');
    }

    /**
     * 获取单个服务状态
     */
    private function getServiceStatus($licenseKeyId, $serviceType) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM ai_service_configs
                WHERE license_key_id = ? AND service_type = ?
            ");
            $stmt->execute([$licenseKeyId, $serviceType]);
            $config = $stmt->fetch();

            if (!$config) {
                return [
                    'configured' => false,
                    'enabled' => false,
                    'status' => 'not_configured',
                    'message' => ucfirst($serviceType) . ' 未配置'
                ];
            }

            // 获取有效API密钥数量
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM ai_api_keys
                WHERE config_id = ? AND is_valid = 1
            ");
            $stmt->execute([$config['id']]);
            $validKeyCount = $stmt->fetchColumn();

            $status = 'ready';
            $message = ucfirst($serviceType) . ' 已就绪';

            if (!$config['enabled']) {
                $status = 'disabled';
                $message = ucfirst($serviceType) . ' 未启用';
            } elseif ($validKeyCount == 0) {
                $status = 'no_valid_keys';
                $message = ucfirst($serviceType) . ' 没有有效的API密钥';
            }

            return [
                'configured' => true,
                'enabled' => (bool)$config['enabled'],
                'status' => $status,
                'message' => $message,
                'model' => $config['model'],
                'valid_api_keys' => (int)$validKeyCount,
                'thinking_enabled' => (bool)$config['thinking_enabled'],
                'reply_delay' => (int)$config['reply_delay']
            ];

        } catch (PDOException $e) {
            error_log("Get service status error: " . $e->getMessage());
            return [
                'configured' => false,
                'enabled' => false,
                'status' => 'error',
                'message' => '状态获取失败'
            ];
        }
    }

    /**
     * 获取单个服务配置
     */
    private function getServiceConfig($licenseKeyId, $serviceType) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM ai_service_configs
                WHERE license_key_id = ? AND service_type = ?
            ");
            $stmt->execute([$licenseKeyId, $serviceType]);
            $config = $stmt->fetch();

            if (!$config) {
                return null;
            }

            // 获取API密钥（脱敏）
            $stmt = $this->db->prepare("
                SELECT id, is_valid, last_validated_at, usage_count, created_at
                FROM ai_api_keys
                WHERE config_id = ?
                ORDER BY created_at DESC
            ");
            $stmt->execute([$config['id']]);
            $apiKeys = $stmt->fetchAll();

            return [
                'enabled' => (bool)$config['enabled'],
                'model' => $config['model'],
                'thinking_enabled' => (bool)$config['thinking_enabled'],
                'reply_delay' => (int)$config['reply_delay'],
                'system_prompt' => $config['system_prompt'],
                'current_api_key_index' => (int)$config['current_api_key_index'],
                'api_keys' => array_map(function($key) {
                    return [
                        'id' => $key['id'],
                        'is_valid' => (bool)$key['is_valid'],
                        'last_validated_at' => $key['last_validated_at'],
                        'usage_count' => (int)$key['usage_count'],
                        'created_at' => $key['created_at']
                    ];
                }, $apiKeys),
                'created_at' => $config['created_at'],
                'updated_at' => $config['updated_at']
            ];

        } catch (PDOException $e) {
            error_log("Get service config error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 计算整体状态
     */
    private function calculateOverallStatus($deepseekStatus, $doubaoStatus) {
        $enabledServices = 0;
        $readyServices = 0;

        if ($deepseekStatus['enabled']) {
            $enabledServices++;
            if ($deepseekStatus['status'] === 'ready') {
                $readyServices++;
            }
        }

        if ($doubaoStatus['enabled']) {
            $enabledServices++;
            if ($doubaoStatus['status'] === 'ready') {
                $readyServices++;
            }
        }

        if ($enabledServices === 0) {
            return [
                'status' => 'all_disabled',
                'message' => '所有AI服务都未启用',
                'enabled_services' => 0,
                'ready_services' => 0
            ];
        }

        if ($readyServices === $enabledServices) {
            return [
                'status' => 'all_ready',
                'message' => '所有启用的AI服务都已就绪',
                'enabled_services' => $enabledServices,
                'ready_services' => $readyServices
            ];
        }

        if ($readyServices > 0) {
            return [
                'status' => 'partial_ready',
                'message' => '部分AI服务已就绪',
                'enabled_services' => $enabledServices,
                'ready_services' => $readyServices
            ];
        }

        return [
            'status' => 'none_ready',
            'message' => '没有AI服务就绪',
            'enabled_services' => $enabledServices,
            'ready_services' => $readyServices
        ];
    }

    /**
     * 计算使用统计
     */
    private function calculateUsageStats($licenseKeyId) {
        try {
            $stats = [];

            // 获取今日统计
            $stmt = $this->db->prepare("
                SELECT
                    asc.service_type,
                    COUNT(asl.id) as total_requests,
                    SUM(CASE WHEN asl.status = 'success' THEN 1 ELSE 0 END) as successful_requests,
                    SUM(CASE WHEN asl.status = 'error' THEN 1 ELSE 0 END) as failed_requests,
                    AVG(asl.response_time) as avg_response_time
                FROM ai_service_configs asc
                LEFT JOIN ai_service_logs asl ON asc.id = asl.config_id
                    AND DATE(asl.created_at) = CURDATE()
                WHERE asc.license_key_id = ?
                GROUP BY asc.service_type
            ");
            $stmt->execute([$licenseKeyId]);
            $todayStats = $stmt->fetchAll();

            foreach ($todayStats as $stat) {
                $stats['today'][$stat['service_type']] = [
                    'total_requests' => (int)$stat['total_requests'],
                    'successful_requests' => (int)$stat['successful_requests'],
                    'failed_requests' => (int)$stat['failed_requests'],
                    'success_rate' => $stat['total_requests'] > 0 ?
                        round($stat['successful_requests'] / $stat['total_requests'] * 100, 2) : 0,
                    'avg_response_time' => round($stat['avg_response_time'] ?? 0, 2)
                ];
            }

            // 获取本月统计
            $stmt = $this->db->prepare("
                SELECT
                    asc.service_type,
                    COUNT(asl.id) as total_requests,
                    SUM(CASE WHEN asl.status = 'success' THEN 1 ELSE 0 END) as successful_requests
                FROM ai_service_configs asc
                LEFT JOIN ai_service_logs asl ON asc.id = asl.config_id
                    AND YEAR(asl.created_at) = YEAR(CURDATE())
                    AND MONTH(asl.created_at) = MONTH(CURDATE())
                WHERE asc.license_key_id = ?
                GROUP BY asc.service_type
            ");
            $stmt->execute([$licenseKeyId]);
            $monthStats = $stmt->fetchAll();

            foreach ($monthStats as $stat) {
                $stats['this_month'][$stat['service_type']] = [
                    'total_requests' => (int)$stat['total_requests'],
                    'successful_requests' => (int)$stat['successful_requests']
                ];
            }

            return $stats;

        } catch (PDOException $e) {
            error_log("Calculate usage stats error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 同步服务配置
     */
    private function syncServiceConfig($licenseKeyId, $serviceType, $configData) {
        try {
            // 获取或创建配置
            $stmt = $this->db->prepare("
                SELECT id FROM ai_service_configs
                WHERE license_key_id = ? AND service_type = ?
            ");
            $stmt->execute([$licenseKeyId, $serviceType]);
            $config = $stmt->fetch();

            if ($config) {
                // 更新现有配置
                $stmt = $this->db->prepare("
                    UPDATE ai_service_configs
                    SET enabled = ?, model = ?, thinking_enabled = ?, reply_delay = ?,
                        system_prompt = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $configData['enabled'] ?? false,
                    $configData['model'] ?? '',
                    $configData['thinkingEnabled'] ?? false,
                    $configData['replyDelay'] ?? 0,
                    $configData['systemPrompt'] ?? '',
                    $config['id']
                ]);
                $configId = $config['id'];
            } else {
                // 创建新配置
                $stmt = $this->db->prepare("
                    INSERT INTO ai_service_configs
                    (license_key_id, service_type, enabled, model, thinking_enabled, reply_delay, system_prompt)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $licenseKeyId,
                    $serviceType,
                    $configData['enabled'] ?? false,
                    $configData['model'] ?? '',
                    $configData['thinkingEnabled'] ?? false,
                    $configData['replyDelay'] ?? 0,
                    $configData['systemPrompt'] ?? ''
                ]);
                $configId = $this->db->lastInsertId();
            }

            // 同步API密钥
            $syncedKeys = 0;
            if (isset($configData['apiKeys']) && is_array($configData['apiKeys'])) {
                foreach ($configData['apiKeys'] as $index => $apiKey) {
                    if (!empty($apiKey)) {
                        $this->syncApiKey($configId, $apiKey, $serviceType);
                        $syncedKeys++;
                    }
                }
            }

            return [
                'success' => true,
                'config_id' => $configId,
                'synced_keys' => $syncedKeys
            ];

        } catch (Exception $e) {
            error_log("Sync service config error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 同步API密钥
     */
    private function syncApiKey($configId, $apiKey, $serviceType) {
        try {
            $keyHash = hash('sha256', $apiKey);

            // 检查密钥是否已存在
            $stmt = $this->db->prepare("
                SELECT id FROM ai_api_keys
                WHERE config_id = ? AND api_key_hash = ?
            ");
            $stmt->execute([$configId, $keyHash]);
            if ($stmt->fetch()) {
                return; // 密钥已存在，跳过
            }

            // 加密API密钥
            $encryptedKey = $this->encryptApiKey($apiKey);

            // 验证API密钥
            $isValid = $this->validateApiKeyForService($apiKey, $serviceType);

            // 保存API密钥
            $stmt = $this->db->prepare("
                INSERT INTO ai_api_keys
                (config_id, api_key, api_key_hash, is_valid, last_validated_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$configId, $encryptedKey, $keyHash, $isValid ? 1 : 0]);

        } catch (Exception $e) {
            error_log("Sync API key error: " . $e->getMessage());
        }
    }

    /**
     * 验证服务的所有API密钥
     */
    private function validateServiceApiKeys($licenseKeyId, $serviceType) {
        try {
            $stmt = $this->db->prepare("
                SELECT aak.id, aak.api_key
                FROM ai_service_configs asc
                JOIN ai_api_keys aak ON asc.id = aak.config_id
                WHERE asc.license_key_id = ? AND asc.service_type = ?
            ");
            $stmt->execute([$licenseKeyId, $serviceType]);
            $keys = $stmt->fetchAll();

            $results = [];
            foreach ($keys as $key) {
                $decryptedKey = $this->decryptApiKey($key['api_key']);
                $isValid = $this->validateApiKeyForService($decryptedKey, $serviceType);

                // 更新验证状态
                $stmt = $this->db->prepare("
                    UPDATE ai_api_keys
                    SET is_valid = ?, last_validated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$isValid ? 1 : 0, $key['id']]);

                $results[] = [
                    'key_id' => $key['id'],
                    'is_valid' => $isValid
                ];
            }

            return $results;

        } catch (Exception $e) {
            error_log("Validate service API keys error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 验证特定服务的API密钥
     */
    private function validateApiKeyForService($apiKey, $serviceType) {
        // 这里可以调用具体的验证逻辑
        // 为了简化，这里返回true，实际应该调用对应的API验证
        return true;
    }

    /**
     * 加密API密钥
     */
    private function encryptApiKey($apiKey) {
        $method = 'AES-256-CBC';
        $key = hash('sha256', $this->config->getApiSecretKey());
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($apiKey, $method, $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }

    /**
     * 解密API密钥
     */
    private function decryptApiKey($encryptedApiKey) {
        $method = 'AES-256-CBC';
        $key = hash('sha256', $this->config->getApiSecretKey());
        $data = base64_decode($encryptedApiKey);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, $method, $key, 0, $iv);
    }
}

// 创建API实例并处理请求
new AIServiceManager();
