-- =====================================================
-- AI客服设置数据库表结构
-- 版本：v1.0
-- 日期：2025-08-17
-- 描述：为DeepSeek和豆包AI客服功能创建独立的数据库表
-- =====================================================

SET FOREIGN_KEY_CHECKS = 0;

-- 1. AI服务配置表（主表）
DROP TABLE IF EXISTS `ai_service_configs`;
CREATE TABLE `ai_service_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key_id` int(11) NOT NULL COMMENT '关联的卡密ID',
  `service_type` enum('deepseek','doubao') NOT NULL COMMENT 'AI服务类型',
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `model` varchar(100) NOT NULL COMMENT '使用的模型',
  `thinking_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用深度思考',
  `reply_delay` int(11) NOT NULL DEFAULT 0 COMMENT '回复延迟（秒）',
  `system_prompt` text COMMENT '系统提示词',
  `current_api_key_index` int(11) NOT NULL DEFAULT 0 COMMENT '当前使用的API密钥索引',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_license_service` (`license_key_id`, `service_type`),
  KEY `idx_license_key_id` (`license_key_id`),
  KEY `idx_service_type` (`service_type`),
  KEY `idx_enabled` (`enabled`),
  CONSTRAINT `fk_ai_configs_license` FOREIGN KEY (`license_key_id`) REFERENCES `license_keys` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务配置表';

-- 2. AI API密钥表
DROP TABLE IF EXISTS `ai_api_keys`;
CREATE TABLE `ai_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_id` int(11) NOT NULL COMMENT '关联的AI配置ID',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥（加密存储）',
  `api_key_hash` varchar(64) NOT NULL COMMENT 'API密钥哈希值（用于查重）',
  `is_valid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '密钥是否有效',
  `last_validated_at` timestamp NULL DEFAULT NULL COMMENT '最后验证时间',
  `validation_error` text COMMENT '验证错误信息',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_config_hash` (`config_id`, `api_key_hash`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_is_valid` (`is_valid`),
  KEY `idx_last_validated` (`last_validated_at`),
  CONSTRAINT `fk_api_keys_config` FOREIGN KEY (`config_id`) REFERENCES `ai_service_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API密钥表';

-- 3. AI服务使用日志表
DROP TABLE IF EXISTS `ai_service_logs`;
CREATE TABLE `ai_service_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_id` int(11) NOT NULL COMMENT '关联的AI配置ID',
  `api_key_id` int(11) DEFAULT NULL COMMENT '使用的API密钥ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型（chat, validate, test等）',
  `request_data` json COMMENT '请求数据',
  `response_data` json COMMENT '响应数据',
  `status` enum('success','error','timeout') NOT NULL COMMENT '状态',
  `error_message` text COMMENT '错误信息',
  `response_time` int(11) COMMENT '响应时间（毫秒）',
  `ip_address` varchar(45) COMMENT '请求IP',
  `user_agent` varchar(500) COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_api_key_id` (`api_key_id`),
  KEY `idx_action` (`action`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_logs_config` FOREIGN KEY (`config_id`) REFERENCES `ai_service_configs` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_logs_api_key` FOREIGN KEY (`api_key_id`) REFERENCES `ai_api_keys` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务使用日志表';

-- 4. AI服务统计表
DROP TABLE IF EXISTS `ai_service_stats`;
CREATE TABLE `ai_service_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_id` int(11) NOT NULL COMMENT '关联的AI配置ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_requests` int(11) NOT NULL DEFAULT 0 COMMENT '总请求数',
  `successful_requests` int(11) NOT NULL DEFAULT 0 COMMENT '成功请求数',
  `failed_requests` int(11) NOT NULL DEFAULT 0 COMMENT '失败请求数',
  `avg_response_time` decimal(10,2) COMMENT '平均响应时间（毫秒）',
  `total_tokens_used` int(11) NOT NULL DEFAULT 0 COMMENT '总使用token数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_config_date` (`config_id`, `date`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_date` (`date`),
  CONSTRAINT `fk_stats_config` FOREIGN KEY (`config_id`) REFERENCES `ai_service_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务统计表';

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 插入默认数据
-- =====================================================

-- 为现有的license_keys创建默认AI配置（如果需要）
-- 注意：这里只是示例，实际使用时需要根据具体的license_key_id来插入

-- 插入默认的DeepSeek配置模板
INSERT IGNORE INTO `ai_service_configs` 
(`license_key_id`, `service_type`, `enabled`, `model`, `thinking_enabled`, `reply_delay`, `system_prompt`) 
SELECT 
    1 as license_key_id,
    'deepseek' as service_type,
    0 as enabled,
    'deepseek-chat' as model,
    0 as thinking_enabled,
    0 as reply_delay,
    '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。' as system_prompt
WHERE EXISTS (SELECT 1 FROM `license_keys` WHERE `id` = 1);

-- 插入默认的豆包配置模板
INSERT IGNORE INTO `ai_service_configs` 
(`license_key_id`, `service_type`, `enabled`, `model`, `thinking_enabled`, `reply_delay`, `system_prompt`) 
SELECT 
    1 as license_key_id,
    'doubao' as service_type,
    0 as enabled,
    'doubao-seed-1-6-250615' as model,
    0 as thinking_enabled,
    0 as reply_delay,
    '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。' as system_prompt
WHERE EXISTS (SELECT 1 FROM `license_keys` WHERE `id` = 1);

-- =====================================================
-- 创建视图
-- =====================================================

-- AI配置详情视图
DROP VIEW IF EXISTS `v_ai_service_details`;
CREATE VIEW `v_ai_service_details` AS
SELECT 
    c.id as config_id,
    c.license_key_id,
    lk.key_value,
    lk.store_name,
    c.service_type,
    c.enabled,
    c.model,
    c.thinking_enabled,
    c.reply_delay,
    c.system_prompt,
    c.current_api_key_index,
    COUNT(ak.id) as api_key_count,
    SUM(CASE WHEN ak.is_valid = 1 THEN 1 ELSE 0 END) as valid_api_key_count,
    c.created_at,
    c.updated_at
FROM `ai_service_configs` c
LEFT JOIN `license_keys` lk ON c.license_key_id = lk.id
LEFT JOIN `ai_api_keys` ak ON c.id = ak.config_id
GROUP BY c.id;

-- =====================================================
-- 创建存储过程
-- =====================================================

DELIMITER //

-- 获取有效的API密钥
DROP PROCEDURE IF EXISTS `GetValidApiKey`//
CREATE PROCEDURE `GetValidApiKey`(
    IN p_config_id INT,
    OUT p_api_key VARCHAR(500),
    OUT p_api_key_id INT
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_api_key VARCHAR(500);
    DECLARE v_api_key_id INT;
    DECLARE cur CURSOR FOR 
        SELECT id, api_key 
        FROM ai_api_keys 
        WHERE config_id = p_config_id AND is_valid = 1 
        ORDER BY last_used_at ASC, id ASC;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    SET p_api_key = NULL;
    SET p_api_key_id = NULL;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO v_api_key_id, v_api_key;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET p_api_key = v_api_key;
        SET p_api_key_id = v_api_key_id;
        LEAVE read_loop;
    END LOOP;
    CLOSE cur;
    
    -- 更新使用时间
    IF p_api_key_id IS NOT NULL THEN
        UPDATE ai_api_keys 
        SET usage_count = usage_count + 1, last_used_at = NOW() 
        WHERE id = p_api_key_id;
    END IF;
END//

DELIMITER ;

-- =====================================================
-- 数据库表创建完成
-- =====================================================

SELECT 'AI客服设置数据库表创建完成！' as status,
       '已创建4个表：ai_service_configs, ai_api_keys, ai_service_logs, ai_service_stats' as tables,
       '已创建1个视图：v_ai_service_details' as views,
       '已创建1个存储过程：GetValidApiKey' as procedures;
