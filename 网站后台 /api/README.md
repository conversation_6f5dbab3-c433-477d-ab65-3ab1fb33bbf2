# AI客服API接口文档

## 概述

本项目为网站后台AI客服系统提供了完整的API接口，支持DeepSeek和豆包两种AI服务的独立管理。每个服务都有独立的API接口，同时提供统一的服务管理器。

## 功能特性

- ✅ **独立API接口** - DeepSeek和豆包各有独立的API接口
- ✅ **权限验证** - 基于license key的完整权限验证系统
- ✅ **配置管理** - 支持启用/关闭、模型选择、系统提示词等配置
- ✅ **API密钥管理** - 支持多个API密钥的添加、验证、删除
- ✅ **加密存储** - API密钥采用AES-256-CBC加密存储
- ✅ **使用统计** - 完整的使用日志和统计功能
- ✅ **速率限制** - 防止API滥用的速率限制机制
- ✅ **前端集成** - 与现有AI设置页面完美集成

## 文件结构

```
api/
├── README.md                    # 本文档
├── ai_settings_database.sql     # 数据库表结构
├── setup_ai_database.php       # 数据库设置脚本
├── ai_auth_middleware.php       # 权限验证中间件
├── deepseek_api.php            # DeepSeek API接口
├── doubao_api.php              # 豆包API接口
├── ai_service_manager.php      # AI服务管理器
├── test_ai_apis.php            # API测试脚本
└── ai_api_examples.php         # API使用示例页面
```

## 快速开始

### 1. 数据库设置

首先运行数据库设置脚本：

```bash
# 访问设置页面
http://your-domain/网站后台 /api/setup_ai_database.php
```

或者直接执行SQL文件：

```sql
-- 导入数据库表结构
SOURCE ai_settings_database.sql;
```

### 2. 配置验证

访问API示例页面验证配置：

```bash
http://your-domain/网站后台 /api/ai_api_examples.php
```

### 3. 测试API接口

运行测试脚本：

```bash
http://your-domain/网站后台 /api/test_ai_apis.php
```

## API接口文档

### DeepSeek API (`deepseek_api.php`)

#### 获取配置
```http
GET /api/deepseek_api.php?action=config&license_key=YOUR_LICENSE_KEY
```

#### 更新配置
```http
PUT /api/deepseek_api.php?action=config
Content-Type: application/json

{
    "license_key": "YOUR_LICENSE_KEY",
    "enabled": true,
    "model": "deepseek-chat",
    "thinking_enabled": false,
    "reply_delay": 0,
    "system_prompt": "你是一名专业的客服人员。"
}
```

#### 添加API密钥
```http
PUT /api/deepseek_api.php?action=add_api_key
Content-Type: application/json

{
    "license_key": "YOUR_LICENSE_KEY",
    "api_key": "sk-your-deepseek-api-key"
}
```

#### 发送聊天消息
```http
POST /api/deepseek_api.php?action=chat
Content-Type: application/json

{
    "license_key": "YOUR_LICENSE_KEY",
    "message": "你好，我需要帮助",
    "session_id": "user_session_123",
    "context": [
        {"role": "user", "content": "之前的消息"},
        {"role": "assistant", "content": "之前的回复"}
    ]
}
```

### 豆包API (`doubao_api.php`)

豆包API的接口格式与DeepSeek API相同，只需要将URL中的`deepseek_api.php`替换为`doubao_api.php`。

支持的模型：
- `doubao-seed-1-6-250615` - 通用对话模型
- `doubao-1.5-vision-pro-250328` - 多模态模型
- `doubao-seed-1-6-thinking-250715` - 支持深度思考的模型

### AI服务管理器 (`ai_service_manager.php`)

#### 获取所有服务状态
```http
GET /api/ai_service_manager.php?action=status&license_key=YOUR_LICENSE_KEY
```

#### 获取所有配置
```http
GET /api/ai_service_manager.php?action=configs&license_key=YOUR_LICENSE_KEY
```

#### 同步前端配置
```http
POST /api/ai_service_manager.php?action=sync_from_frontend
Content-Type: application/json

{
    "license_key": "YOUR_LICENSE_KEY",
    "ai_settings": {
        "deepseek": {
            "enabled": true,
            "model": "deepseek-chat",
            "thinkingEnabled": false,
            "replyDelay": 0,
            "systemPrompt": "你是一名专业的客服人员。",
            "apiKeys": ["sk-your-deepseek-key"]
        },
        "doubao": {
            "enabled": true,
            "model": "doubao-seed-1-6-250615",
            "thinkingEnabled": false,
            "replyDelay": 0,
            "systemPrompt": "你是一名专业的客服人员。",
            "apiKeys": ["your-doubao-key"]
        }
    }
}
```

## 响应格式

所有API都返回统一的JSON格式：

### 成功响应
```json
{
    "success": true,
    "message": "操作成功",
    "data": {
        // 具体数据
    }
}
```

### 错误响应
```json
{
    "success": false,
    "message": "错误描述",
    "error_code": "ERROR_CODE"
}
```

## 错误代码

| 错误代码 | 描述 |
|---------|------|
| `INVALID_LICENSE` | 无效的license key |
| `NO_PERMISSION` | 没有AI客服功能权限 |
| `SERVICE_DISABLED` | AI服务未启用 |
| `NO_VALID_API_KEY` | 没有有效的API密钥 |
| `RATE_LIMIT_EXCEEDED` | 请求频率超限 |
| `VALIDATION_ERROR` | 权限验证失败 |

## 数据库表结构

### ai_service_configs
AI服务配置表，存储每个license key的AI服务配置。

### ai_api_keys
API密钥表，存储加密的API密钥和验证状态。

### ai_service_logs
服务使用日志表，记录所有API调用。

### ai_service_stats
服务统计表，存储每日使用统计。

## 安全特性

1. **API密钥加密** - 使用AES-256-CBC加密存储
2. **权限验证** - 基于license key的多层权限验证
3. **速率限制** - 防止API滥用
4. **日志记录** - 完整的操作日志
5. **输入验证** - 严格的输入参数验证

## 前端集成

AI设置页面已经集成了新的API接口：

1. **自动加载** - 页面加载时自动从服务器获取配置
2. **实时保存** - 配置更改时自动保存到服务器
3. **状态同步** - 前端状态与服务器数据保持同步
4. **错误处理** - 完善的错误提示和处理

## 使用限制

- 每分钟聊天请求：60次
- 每小时聊天请求：1000次
- 每分钟API密钥验证：10次
- 每小时API密钥验证：100次

## 故障排除

### 1. API返回403错误
检查license key是否有效，是否有AI客服功能权限。

### 2. API返回500错误
检查数据库连接，查看错误日志。

### 3. 聊天功能不可用
确保AI服务已启用，且有有效的API密钥。

### 4. 配置保存失败
检查数据库表是否正确创建，权限是否正确。

## 开发者支持

如需技术支持或有问题反馈，请联系开发团队。

## 更新日志

### v1.0.0 (2025-08-17)
- 初始版本发布
- 支持DeepSeek和豆包AI服务
- 完整的API接口和权限验证
- 前端集成和数据库支持
