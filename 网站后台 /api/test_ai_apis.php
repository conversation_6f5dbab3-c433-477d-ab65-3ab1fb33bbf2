<?php
/**
 * AI API接口测试脚本
 * 用于测试DeepSeek和豆包API接口的功能
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 测试配置
$testConfig = [
    'base_url' => 'http://localhost/网站后台 /api',
    'license_key' => 'test_license_key_123', // 请替换为有效的license key
    'deepseek_api_key' => 'sk-test-deepseek-key', // 请替换为有效的DeepSeek API key
    'doubao_api_key' => 'test-doubao-key' // 请替换为有效的豆包API key
];

/**
 * 发送HTTP请求
 */
function sendRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // 设置请求方法
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }
    
    // 设置请求数据
    if ($data) {
        if (is_array($data)) {
            $data = json_encode($data);
            $headers[] = 'Content-Type: application/json';
        }
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    }
    
    // 设置请求头
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['success' => false, 'error' => $error];
    }
    
    $responseData = json_decode($response, true);
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'data' => $responseData
    ];
}

/**
 * 测试结果输出
 */
function outputTestResult($testName, $result) {
    echo "\n=== $testName ===\n";
    echo "状态: " . ($result['success'] ? '✅ 成功' : '❌ 失败') . "\n";
    echo "HTTP状态码: " . $result['http_code'] . "\n";
    
    if (isset($result['data'])) {
        echo "响应数据: " . json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    if (isset($result['error'])) {
        echo "错误信息: " . $result['error'] . "\n";
    }
    
    echo str_repeat('-', 50) . "\n";
}

// 开始测试
echo "开始AI API接口测试...\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat('=', 50) . "\n";

// 1. 测试AI服务管理器 - 获取状态
$result = sendRequest(
    $testConfig['base_url'] . '/ai_service_manager.php?action=status&license_key=' . $testConfig['license_key']
);
outputTestResult('AI服务管理器 - 获取状态', $result);

// 2. 测试AI服务管理器 - 获取配置
$result = sendRequest(
    $testConfig['base_url'] . '/ai_service_manager.php?action=configs&license_key=' . $testConfig['license_key']
);
outputTestResult('AI服务管理器 - 获取配置', $result);

// 3. 测试DeepSeek API - 获取信息
$result = sendRequest(
    $testConfig['base_url'] . '/deepseek_api.php?action=info&license_key=' . $testConfig['license_key']
);
outputTestResult('DeepSeek API - 获取信息', $result);

// 4. 测试DeepSeek API - 获取配置
$result = sendRequest(
    $testConfig['base_url'] . '/deepseek_api.php?action=config&license_key=' . $testConfig['license_key']
);
outputTestResult('DeepSeek API - 获取配置', $result);

// 5. 测试DeepSeek API - 获取状态
$result = sendRequest(
    $testConfig['base_url'] . '/deepseek_api.php?action=status&license_key=' . $testConfig['license_key']
);
outputTestResult('DeepSeek API - 获取状态', $result);

// 6. 测试DeepSeek API - 获取模型列表
$result = sendRequest(
    $testConfig['base_url'] . '/deepseek_api.php?action=models&license_key=' . $testConfig['license_key']
);
outputTestResult('DeepSeek API - 获取模型列表', $result);

// 7. 测试豆包API - 获取信息
$result = sendRequest(
    $testConfig['base_url'] . '/doubao_api.php?action=info&license_key=' . $testConfig['license_key']
);
outputTestResult('豆包API - 获取信息', $result);

// 8. 测试豆包API - 获取配置
$result = sendRequest(
    $testConfig['base_url'] . '/doubao_api.php?action=config&license_key=' . $testConfig['license_key']
);
outputTestResult('豆包API - 获取配置', $result);

// 9. 测试豆包API - 获取状态
$result = sendRequest(
    $testConfig['base_url'] . '/doubao_api.php?action=status&license_key=' . $testConfig['license_key']
);
outputTestResult('豆包API - 获取状态', $result);

// 10. 测试豆包API - 获取模型列表
$result = sendRequest(
    $testConfig['base_url'] . '/doubao_api.php?action=models&license_key=' . $testConfig['license_key']
);
outputTestResult('豆包API - 获取模型列表', $result);

// 11. 测试DeepSeek API - 更新配置
$configData = [
    'license_key' => $testConfig['license_key'],
    'enabled' => true,
    'model' => 'deepseek-chat',
    'thinking_enabled' => false,
    'reply_delay' => 0,
    'system_prompt' => '你是一名专业的客服人员。'
];
$result = sendRequest(
    $testConfig['base_url'] . '/deepseek_api.php?action=config',
    'PUT',
    $configData
);
outputTestResult('DeepSeek API - 更新配置', $result);

// 12. 测试豆包API - 更新配置
$configData = [
    'license_key' => $testConfig['license_key'],
    'enabled' => true,
    'model' => 'doubao-seed-1-6-250615',
    'thinking_enabled' => false,
    'reply_delay' => 0,
    'system_prompt' => '你是一名专业的客服人员。'
];
$result = sendRequest(
    $testConfig['base_url'] . '/doubao_api.php?action=config',
    'PUT',
    $configData
);
outputTestResult('豆包API - 更新配置', $result);

// 13. 测试DeepSeek API - 添加API密钥
$keyData = [
    'license_key' => $testConfig['license_key'],
    'api_key' => $testConfig['deepseek_api_key']
];
$result = sendRequest(
    $testConfig['base_url'] . '/deepseek_api.php?action=add_api_key',
    'PUT',
    $keyData
);
outputTestResult('DeepSeek API - 添加API密钥', $result);

// 14. 测试豆包API - 添加API密钥
$keyData = [
    'license_key' => $testConfig['license_key'],
    'api_key' => $testConfig['doubao_api_key']
];
$result = sendRequest(
    $testConfig['base_url'] . '/doubao_api.php?action=add_api_key',
    'PUT',
    $keyData
);
outputTestResult('豆包API - 添加API密钥', $result);

// 15. 测试AI服务管理器 - 同步前端配置
$syncData = [
    'license_key' => $testConfig['license_key'],
    'ai_settings' => [
        'deepseek' => [
            'enabled' => true,
            'model' => 'deepseek-chat',
            'thinkingEnabled' => false,
            'replyDelay' => 0,
            'systemPrompt' => '你是一名专业的客服人员。',
            'apiKeys' => [$testConfig['deepseek_api_key']]
        ],
        'doubao' => [
            'enabled' => true,
            'model' => 'doubao-seed-1-6-250615',
            'thinkingEnabled' => false,
            'replyDelay' => 0,
            'systemPrompt' => '你是一名专业的客服人员。',
            'apiKeys' => [$testConfig['doubao_api_key']]
        ]
    ]
];
$result = sendRequest(
    $testConfig['base_url'] . '/ai_service_manager.php?action=sync_from_frontend',
    'POST',
    $syncData
);
outputTestResult('AI服务管理器 - 同步前端配置', $result);

// 16. 测试AI服务管理器 - 获取使用统计
$result = sendRequest(
    $testConfig['base_url'] . '/ai_service_manager.php?action=stats&license_key=' . $testConfig['license_key']
);
outputTestResult('AI服务管理器 - 获取使用统计', $result);

echo "\n测试完成！\n";
echo "测试结束时间: " . date('Y-m-d H:i:s') . "\n";
?>
