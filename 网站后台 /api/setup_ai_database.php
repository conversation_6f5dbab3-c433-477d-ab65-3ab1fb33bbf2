<?php
/**
 * AI数据库设置脚本
 * 用于创建AI相关的数据库表和初始化数据
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入数据库配置
require_once __DIR__ . '/../xuxuemei/config/database.php';

echo "<h1>AI数据库设置</h1>\n";
echo "<p>开始时间: " . date('Y-m-d H:i:s') . "</p>\n";

try {
    // 读取SQL文件
    $sqlFile = __DIR__ . '/ai_settings_database.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("无法读取SQL文件");
    }
    
    echo "<h2>执行SQL脚本...</h2>\n";
    
    // 分割SQL语句
    $statements = explode(';', $sql);
    $executedCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // 跳过空语句和注释
        }
        
        try {
            $pdo->exec($statement);
            $executedCount++;
            echo "<p>✅ 执行成功: " . substr($statement, 0, 50) . "...</p>\n";
        } catch (PDOException $e) {
            $errorCount++;
            echo "<p>❌ 执行失败: " . substr($statement, 0, 50) . "... 错误: " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<h2>执行结果</h2>\n";
    echo "<p>成功执行: $executedCount 条语句</p>\n";
    echo "<p>执行失败: $errorCount 条语句</p>\n";
    
    // 验证表是否创建成功
    echo "<h2>验证表结构</h2>\n";
    $tables = ['ai_service_configs', 'ai_api_keys', 'ai_service_logs', 'ai_service_stats'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "<p>✅ 表 $table 创建成功，包含 " . count($columns) . " 个字段</p>\n";
        } catch (PDOException $e) {
            echo "<p>❌ 表 $table 不存在或有问题: " . $e->getMessage() . "</p>\n";
        }
    }
    
    // 检查视图
    try {
        $stmt = $pdo->query("SELECT * FROM v_ai_service_details LIMIT 1");
        echo "<p>✅ 视图 v_ai_service_details 创建成功</p>\n";
    } catch (PDOException $e) {
        echo "<p>❌ 视图 v_ai_service_details 不存在或有问题: " . $e->getMessage() . "</p>\n";
    }
    
    // 检查存储过程
    try {
        $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = 'GetValidApiKey'");
        $result = $stmt->fetch();
        if ($result) {
            echo "<p>✅ 存储过程 GetValidApiKey 创建成功</p>\n";
        } else {
            echo "<p>❌ 存储过程 GetValidApiKey 不存在</p>\n";
        }
    } catch (PDOException $e) {
        echo "<p>❌ 检查存储过程时出错: " . $e->getMessage() . "</p>\n";
    }
    
    // 创建测试数据
    echo "<h2>创建测试数据</h2>\n";
    
    // 检查是否有license_keys数据
    $stmt = $pdo->query("SELECT COUNT(*) FROM license_keys");
    $licenseCount = $stmt->fetchColumn();
    
    if ($licenseCount > 0) {
        // 为第一个license key创建测试配置
        $stmt = $pdo->query("SELECT id FROM license_keys LIMIT 1");
        $licenseId = $stmt->fetchColumn();
        
        if ($licenseId) {
            // 创建DeepSeek配置
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO ai_service_configs 
                (license_key_id, service_type, enabled, model, thinking_enabled, reply_delay, system_prompt) 
                VALUES (?, 'deepseek', 0, 'deepseek-chat', 0, 0, ?)
            ");
            $defaultPrompt = '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。';
            $stmt->execute([$licenseId, $defaultPrompt]);
            
            // 创建豆包配置
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO ai_service_configs 
                (license_key_id, service_type, enabled, model, thinking_enabled, reply_delay, system_prompt) 
                VALUES (?, 'doubao', 0, 'doubao-seed-1-6-250615', 0, 0, ?)
            ");
            $stmt->execute([$licenseId, $defaultPrompt]);
            
            echo "<p>✅ 为license ID $licenseId 创建了默认AI配置</p>\n";
        }
    } else {
        echo "<p>⚠️ 没有找到license_keys数据，跳过测试配置创建</p>\n";
    }
    
    echo "<h2>设置完成</h2>\n";
    echo "<p>AI数据库设置已完成！</p>\n";
    echo "<p>完成时间: " . date('Y-m-d H:i:s') . "</p>\n";
    
    // 显示下一步操作
    echo "<h2>下一步操作</h2>\n";
    echo "<ol>\n";
    echo "<li>访问 <a href='test_ai_apis.php'>test_ai_apis.php</a> 测试API接口</li>\n";
    echo "<li>在AI设置页面中配置DeepSeek和豆包</li>\n";
    echo "<li>添加有效的API密钥进行测试</li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "<h2>设置失败</h2>\n";
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>\n";
    echo "<p>失败时间: " . date('Y-m-d H:i:s') . "</p>\n";
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>AI数据库设置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        p { margin: 5px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <!-- 内容已在上面输出 -->
</body>
</html>
