<?php
/**
 * API接口修复测试文件
 * 用于测试DeepSeek和豆包API接口是否正常工作
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * 响应成功
 */
function respondSuccess($data = [], $message = 'Success') {
    $response = [
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 响应错误
 */
function respondError($message = 'Error', $code = 400) {
    $response = [
        'success' => false,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    http_response_code($code);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 测试数据库连接
 */
function testDatabaseConnection() {
    try {
        // 数据库配置
        $dbConfig = [
            'host' => '127.0.0.1',
            'port' => '3306',
            'dbname' => 'xiaomeihuakefu_c',
            'user' => 'xiaomeihuakefu_c',
            'pass' => '7Da5F1Xx995cxYz8'
        ];
        
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
        
        // 测试查询
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        
        return [
            'success' => true,
            'message' => '数据库连接成功',
            'test_result' => $result
        ];
        
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => '数据库连接失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 测试DeepSeek API
 */
function testDeepSeekAPI($apiKey = null) {
    if (!$apiKey) {
        return [
            'success' => false,
            'message' => '需要提供DeepSeek API密钥'
        ];
    }
    
    try {
        $url = 'https://api.deepseek.com/v1/chat/completions';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ];
        
        $data = [
            'model' => 'deepseek-chat',
            'messages' => [
                ['role' => 'user', 'content' => '你好，这是一个测试消息']
            ],
            'max_tokens' => 50
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'message' => 'cURL错误: ' . $error
            ];
        }
        
        $responseData = json_decode($response, true);
        
        if ($httpCode === 200 && isset($responseData['choices'])) {
            return [
                'success' => true,
                'message' => 'DeepSeek API测试成功',
                'response' => $responseData['choices'][0]['message']['content'] ?? '无响应内容'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'DeepSeek API测试失败',
                'http_code' => $httpCode,
                'response' => $responseData
            ];
        }
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'DeepSeek API测试异常: ' . $e->getMessage()
        ];
    }
}

/**
 * 测试豆包API
 */
function testDoubaoAPI($apiKey = null) {
    if (!$apiKey) {
        return [
            'success' => false,
            'message' => '需要提供豆包API密钥'
        ];
    }
    
    try {
        $url = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ];
        
        $data = [
            'model' => 'doubao-seed-1-6-250615',
            'messages' => [
                ['role' => 'user', 'content' => '你好，这是一个测试消息']
            ],
            'max_tokens' => 50
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'message' => 'cURL错误: ' . $error
            ];
        }
        
        $responseData = json_decode($response, true);
        
        if ($httpCode === 200 && isset($responseData['choices'])) {
            return [
                'success' => true,
                'message' => '豆包API测试成功',
                'response' => $responseData['choices'][0]['message']['content'] ?? '无响应内容'
            ];
        } else {
            return [
                'success' => false,
                'message' => '豆包API测试失败',
                'http_code' => $httpCode,
                'response' => $responseData
            ];
        }
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '豆包API测试异常: ' . $e->getMessage()
        ];
    }
}

// 处理请求
$action = $_GET['action'] ?? $_POST['action'] ?? 'info';

switch ($action) {
    case 'info':
        respondSuccess([
            'api_name' => 'AI API修复测试接口',
            'version' => '1.0.0',
            'available_actions' => [
                'info' => '获取API信息',
                'test_db' => '测试数据库连接',
                'test_deepseek' => '测试DeepSeek API（需要api_key参数）',
                'test_doubao' => '测试豆包API（需要api_key参数）',
                'test_all' => '测试所有功能（需要deepseek_key和doubao_key参数）'
            ],
            'usage' => [
                'GET /test_api_fix.php?action=info',
                'GET /test_api_fix.php?action=test_db',
                'POST /test_api_fix.php?action=test_deepseek&api_key=YOUR_KEY',
                'POST /test_api_fix.php?action=test_doubao&api_key=YOUR_KEY'
            ]
        ], 'API修复测试接口信息');
        break;
        
    case 'test_db':
        $result = testDatabaseConnection();
        if ($result['success']) {
            respondSuccess($result, $result['message']);
        } else {
            respondError($result['message'], 500);
        }
        break;
        
    case 'test_deepseek':
        $apiKey = $_GET['api_key'] ?? $_POST['api_key'] ?? '';
        $result = testDeepSeekAPI($apiKey);
        if ($result['success']) {
            respondSuccess($result, $result['message']);
        } else {
            respondError($result['message'], 400);
        }
        break;
        
    case 'test_doubao':
        $apiKey = $_GET['api_key'] ?? $_POST['api_key'] ?? '';
        $result = testDoubaoAPI($apiKey);
        if ($result['success']) {
            respondSuccess($result, $result['message']);
        } else {
            respondError($result['message'], 400);
        }
        break;
        
    case 'test_all':
        $deepseekKey = $_GET['deepseek_key'] ?? $_POST['deepseek_key'] ?? '';
        $doubaoKey = $_GET['doubao_key'] ?? $_POST['doubao_key'] ?? '';
        
        $results = [
            'database' => testDatabaseConnection(),
            'deepseek' => testDeepSeekAPI($deepseekKey),
            'doubao' => testDoubaoAPI($doubaoKey)
        ];
        
        $allSuccess = $results['database']['success'] && 
                     $results['deepseek']['success'] && 
                     $results['doubao']['success'];
        
        if ($allSuccess) {
            respondSuccess($results, '所有测试通过');
        } else {
            respondError('部分测试失败，请查看详细结果', 400);
        }
        break;
        
    default:
        respondError('未知的操作类型', 400);
}
?>
