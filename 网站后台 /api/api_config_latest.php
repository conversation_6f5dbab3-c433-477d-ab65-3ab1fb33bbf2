<?php
/**
 * 最新API配置文件 - 2025年版本
 * 包含DeepSeek和豆包的最新API接口地址和模型配置
 * 
 * @version 2.0.0
 * <AUTHOR> Assistant
 * @date 2025-01-20
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'Access denied']);
    exit;
}

class LatestApiConfig {
    
    /**
     * DeepSeek API 最新配置
     */
    const DEEPSEEK_CONFIG = [
        'api_url' => 'https://api.deepseek.com/chat/completions',
        'api_url_v1' => 'https://api.deepseek.com/v1/chat/completions', // 兼容旧版本
        'models' => [
            [
                'id' => 'deepseek-chat',
                'name' => 'DeepSeek Chat (V3-0324)',
                'description' => '最新通用对话模型，指向DeepSeek-V3-0324',
                'supports_thinking' => false,
                'max_tokens' => 8192,
                'context_length' => 32768
            ],
            [
                'id' => 'deepseek-reasoner',
                'name' => 'DeepSeek Reasoner (R1-0528)',
                'description' => '最新推理模型，支持深度思考，指向DeepSeek-R1-0528',
                'supports_thinking' => true,
                'max_tokens' => 8192,
                'context_length' => 32768
            ]
        ],
        'default_model' => 'deepseek-chat',
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer {api_key}'
        ]
    ];
    
    /**
     * 豆包API 最新配置
     */
    const DOUBAO_CONFIG = [
        'api_url' => 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        'models' => [
            [
                'id' => 'doubao-seed-1-6-250615',
                'name' => '豆包 Seed 1.6',
                'description' => '通用对话模型',
                'supports_thinking' => false,
                'max_tokens' => 4096,
                'context_length' => 32768
            ],
            [
                'id' => 'doubao-1.5-vision-pro-250328',
                'name' => '豆包 1.5 Vision Pro',
                'description' => '支持视觉理解的多模态模型',
                'supports_thinking' => false,
                'supports_vision' => true,
                'max_tokens' => 4096,
                'context_length' => 32768
            ],
            [
                'id' => 'doubao-seed-1-6-thinking-250715',
                'name' => '豆包 Seed 1.6 Thinking',
                'description' => '支持深度思考的推理模型',
                'supports_thinking' => true,
                'max_tokens' => 4096,
                'context_length' => 32768
            ]
        ],
        'default_model' => 'doubao-seed-1-6-250615',
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer {api_key}'
        ]
    ];
    
    /**
     * 获取DeepSeek配置
     */
    public static function getDeepSeekConfig() {
        return self::DEEPSEEK_CONFIG;
    }
    
    /**
     * 获取豆包配置
     */
    public static function getDoubaoConfig() {
        return self::DOUBAO_CONFIG;
    }
    
    /**
     * 获取DeepSeek API URL
     */
    public static function getDeepSeekApiUrl($useV1 = false) {
        return $useV1 ? self::DEEPSEEK_CONFIG['api_url_v1'] : self::DEEPSEEK_CONFIG['api_url'];
    }
    
    /**
     * 获取豆包API URL
     */
    public static function getDoubaoApiUrl() {
        return self::DOUBAO_CONFIG['api_url'];
    }
    
    /**
     * 获取DeepSeek支持的模型列表
     */
    public static function getDeepSeekModels() {
        return self::DEEPSEEK_CONFIG['models'];
    }
    
    /**
     * 获取豆包支持的模型列表
     */
    public static function getDoubaoModels() {
        return self::DOUBAO_CONFIG['models'];
    }
    
    /**
     * 验证DeepSeek模型是否有效
     */
    public static function isValidDeepSeekModel($model) {
        $validModels = array_column(self::DEEPSEEK_CONFIG['models'], 'id');
        return in_array($model, $validModels);
    }
    
    /**
     * 验证豆包模型是否有效
     */
    public static function isValidDoubaoModel($model) {
        $validModels = array_column(self::DOUBAO_CONFIG['models'], 'id');
        return in_array($model, $validModels);
    }
    
    /**
     * 获取模型信息
     */
    public static function getModelInfo($service, $modelId) {
        $models = $service === 'deepseek' ? self::getDeepSeekModels() : self::getDoubaoModels();
        
        foreach ($models as $model) {
            if ($model['id'] === $modelId) {
                return $model;
            }
        }
        
        return null;
    }
    
    /**
     * 构建API请求头
     */
    public static function buildHeaders($service, $apiKey) {
        $config = $service === 'deepseek' ? self::DEEPSEEK_CONFIG : self::DOUBAO_CONFIG;
        $headers = [];
        
        foreach ($config['headers'] as $key => $value) {
            $headers[] = $key . ': ' . str_replace('{api_key}', $apiKey, $value);
        }
        
        return $headers;
    }
    
    /**
     * 构建标准聊天请求数据
     */
    public static function buildChatRequest($service, $model, $messages, $options = []) {
        $config = $service === 'deepseek' ? self::DEEPSEEK_CONFIG : self::DOUBAO_CONFIG;
        
        $request = [
            'model' => $model,
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'stream' => $options['stream'] ?? false
        ];
        
        // 如果模型支持思考模式且启用了思考
        $modelInfo = self::getModelInfo($service, $model);
        if ($modelInfo && $modelInfo['supports_thinking'] && ($options['thinking_enabled'] ?? false)) {
            $request['reasoning'] = true;
        }
        
        return $request;
    }
    
    /**
     * 获取所有配置信息
     */
    public static function getAllConfigs() {
        return [
            'deepseek' => self::DEEPSEEK_CONFIG,
            'doubao' => self::DOUBAO_CONFIG,
            'version' => '2.0.0',
            'last_updated' => '2025-01-20',
            'notes' => [
                'deepseek-chat 现在指向 DeepSeek-V3-0324',
                'deepseek-reasoner 现在指向 DeepSeek-R1-0528',
                '豆包API地址保持不变，使用火山引擎v3接口',
                '所有模型都支持最大32K上下文长度'
            ]
        ];
    }
    
    /**
     * 检查API配置是否为最新版本
     */
    public static function checkConfigVersion($currentVersion = '1.0.0') {
        $latestVersion = '2.0.0';
        return version_compare($currentVersion, $latestVersion, '>=');
    }
    
    /**
     * 获取配置更新日志
     */
    public static function getUpdateLog() {
        return [
            '2.0.0' => [
                'date' => '2025-01-20',
                'changes' => [
                    '更新DeepSeek API地址为最新版本',
                    '添加deepseek-reasoner模型支持',
                    '更新模型描述和参数',
                    '增加配置版本检查功能'
                ]
            ],
            '1.0.0' => [
                'date' => '2024-12-01',
                'changes' => [
                    '初始版本配置',
                    '支持基础DeepSeek和豆包API'
                ]
            ]
        ];
    }
}

// 如果直接访问此文件，显示配置信息
if (basename($_SERVER['PHP_SELF']) === 'api_config_latest.php') {
    header('Content-Type: application/json; charset=UTF-8');
    echo json_encode([
        'success' => true,
        'message' => '最新API配置信息',
        'data' => LatestApiConfig::getAllConfigs()
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
